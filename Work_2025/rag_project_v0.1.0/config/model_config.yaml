# Model configuration
llm:
  provider: ollama
  base_url: http://ollama2:11434
  models:
    default: gemma:2b
    alternatives:
      - mistral
      - llama2
  parameters:
    temperature: 0.5
    max_tokens: 500

embeddings:
  provider: ollama
  base_url: http://ollama2:11434
  model: gemma:2b

vectorstore:
  provider: chroma
  persist_directory: ./data/vector_stores/chroma_db
  distance_function: cosine

document_processing:
  chunk_size: 1000
  chunk_overlap: 200
