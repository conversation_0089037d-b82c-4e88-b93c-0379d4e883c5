"""Document ingestion script."""
import os
import argparse
import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.document_processing.loader import DocumentLoader
from src.document_processing.splitter import DocumentSplitter
from src.rag.retriever import DocumentRetriever

def ingest_documents(directory_path: str):
    """
    Ingest documents from a directory into the vector store.
    
    Args:
        directory_path: Path to the directory containing documents
    """
    print(f"Ingesting documents from {directory_path}")
    
    # Initialize components
    document_loader = DocumentLoader()
    document_splitter = DocumentSplitter()
    document_retriever = DocumentRetriever()
    
    # Load documents
    documents = document_loader.load_directory(directory_path)
    print(f"Loaded {len(documents)} documents")
    
    # Split documents
    chunks = document_splitter.split_documents(documents)
    print(f"Split into {len(chunks)} chunks")
    
    # Add to vector store
    document_retriever.vector_store.add_documents(chunks)
    print(f"Added {len(chunks)} chunks to vector store")
    
    # Test retrieval
    test_query = "test query"
    docs_with_scores, _ = document_retriever.get_relevant_documents(test_query, k=1)
    
    if docs_with_scores:
        print(f"\nTest Query: '{test_query}'")
        print(f"Retrieved Document Score: {docs_with_scores[0]['score']:.4f}")
        print(f"Content: {docs_with_scores[0]['content'][:200]}...")
    else:
        print("No documents retrieved for test query.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Ingest documents into the vector store")
    parser.add_argument("--dir", type=str, default="./data/documents", help="Directory containing documents to ingest")
    args = parser.parse_args()
    
    ingest_documents(args.dir)