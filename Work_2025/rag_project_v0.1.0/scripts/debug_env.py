"""Debug script to check environment variables and connections."""
import os
import sys
import requests
import yaml

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

def check_ollama_connection():
    """Check if we can connect to Ollama."""
    # Get the Ollama URL from environment or config
    ollama_url = os.environ.get("OLLAMA_URL")
    
    if not ollama_url:
        # Try to get from config
        try:
            with open("config/model_config.yaml", 'r') as f:
                config = yaml.safe_load(f)
                ollama_url = config.get('llm', {}).get('base_url')
        except Exception as e:
            print(f"Error reading config: {e}")
    
    print(f"OLLAMA_URL from environment or config: {ollama_url}")
    
    # Ensure URL has protocol
    if ollama_url and not ollama_url.startswith(('http://', 'https://')):
        ollama_url = f"http://{ollama_url}"
        print(f"Added protocol to URL: {ollama_url}")
    
    # Try to connect to Ollama
    if ollama_url:
        try:
            response = requests.get(f"{ollama_url}/api/tags")
            print(f"Connection to Ollama successful: {response.status_code}")
            print(f"Available models: {response.json()}")
        except Exception as e:
            print(f"Error connecting to Ollama: {e}")
    else:
        print("OLLAMA_URL not found in environment or config")

if __name__ == "__main__":
    print("Checking environment variables...")
    print(f"PYTHONPATH: {os.environ.get('PYTHONPATH', 'Not set')}")
    print(f"OLLAMA_URL: {os.environ.get('OLLAMA_URL', 'Not set')}")
    
    print("\nChecking Ollama connection...")
    check_ollama_connection()