import os
import time
from pathlib import Path
import sys
sys.path.insert(0, "/app")
from src.document_processing.loader import DocumentLoader
from src.document_processing.splitter import DocumentSplitter
from src.rag.retriever import DocumentRetriever

UPLOAD_DIR = Path("/data/uploads")
POLL_INTERVAL = 5  # seconds
PROCESSED_DIR = UPLOAD_DIR / "processed"

def ensure_directories():
    UPLOAD_DIR.mkdir(parents=True, exist_ok=True)
    PROCESSED_DIR.mkdir(parents=True, exist_ok=True)

def get_new_files():
    return [f for f in UPLOAD_DIR.glob("*") if f.is_file() and not (PROCESSED_DIR / f.name).exists()]

def process_file(file_path: Path):
    print(f"\n📄 Processing file: {file_path.name}")

    loader = DocumentLoader()
    splitter = DocumentSplitter()
    retriever = DocumentRetriever()

    documents = loader.load_file(str(file_path))
    print(f"🔍 Loaded {len(documents)} documents")

    chunks = splitter.split_documents(documents)
    print(f"✂️ Split into {len(chunks)} chunks")

    retriever.vector_store.add_documents(chunks)
    print(f"✅ Ingested {len(chunks)} chunks to vector store")

    # Move to processed
    processed_path = PROCESSED_DIR / file_path.name
    file_path.rename(processed_path)
    print(f"📁 Moved {file_path.name} to processed/")

def watch_uploads():
    ensure_directories()
    print(f"👀 Watching {UPLOAD_DIR} for new files...\n")

    try:
        while True:
            new_files = get_new_files()
            for file_path in new_files:
                process_file(file_path)
            time.sleep(POLL_INTERVAL)
    except KeyboardInterrupt:
        print("👋 Stopped watching.")

if __name__ == "__main__":
    watch_uploads()
