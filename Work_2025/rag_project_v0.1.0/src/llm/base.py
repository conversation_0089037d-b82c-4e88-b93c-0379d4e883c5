"""Base LLM client interface."""
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

class BaseLLM(ABC):
    """Base class for LLM clients."""
    
    @abstractmethod
    def __init__(self, config: Dict[str, Any]):
        """Initialize the LLM client with configuration."""
        pass
    
    @abstractmethod
    def generate(self, prompt: str, **kwargs) -> str:
        """Generate text from a prompt."""
        pass
    
    @abstractmethod
    def embed(self, text: str) -> list:
        """Generate embeddings for text."""
        pass