"""Document loading functionality."""
import os
from typing import List, Optional
import yaml

from langchain_community.document_loaders import PyPDFLoader, TextLoader, DirectoryLoader
from langchain_core.documents import Document

class DocumentLoader:
    """Loads documents from various sources."""
    
    def __init__(self, config_path: str = "config/model_config.yaml"):
        """Initialize the document loader."""
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        self.config = config
    
    def load_file(self, file_path: str) -> List[Document]:
        """
        Load a single file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            List of documents
        """
        if file_path.endswith('.pdf'):
            loader = PyPDFLoader(file_path)
            return loader.load()
        elif file_path.endswith('.txt'):
            loader = TextLoader(file_path)
            return loader.load()
        else:
            raise ValueError(f"Unsupported file type: {file_path}")
    
    def load_directory(self, directory_path: str) -> List[Document]:
        documents = []
        
        # Load PDFs
        pdf_loader = DirectoryLoader(
            directory_path,
            glob="**/*.pdf",
            loader_cls=PyPDFLoader,
        )
        documents.extend(pdf_loader.load())
        
        # Load text files
        txt_loader = DirectoryLoader(
            directory_path,
            glob="**/*.txt",
            loader_cls=TextLoader,
        )
        documents.extend(txt_loader.load())
        
        return documents

    
    def save_uploaded_file(self, uploaded_file, upload_dir: str = "./data/uploads") -> str:
        """
        Save an uploaded file and return its path.
        
        Args:
            uploaded_file: The uploaded file object
            upload_dir: Directory to save the file
            
        Returns:
            Path to the saved file
        """
        # Create upload directory if it doesn't exist
        os.makedirs(upload_dir, exist_ok=True)
        
        # Save the file
        file_path = os.path.join(upload_dir, uploaded_file.name)
        with open(file_path, "wb") as f:
            f.write(uploaded_file.getbuffer())
        
        return file_path