# Environment variables
.env
.env.*
*.env

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
ENV/
env/
.venv/

# Docker volumes
data/
data/chroma_db/
data/uploads/
data/documents/
data/vector_stores/

# Logs
logs/
*.log

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
*~

# Jupyter Notebook
.ipynb_checkpoints

# Local configuration
config.local.yaml

# Model files
*.bin
*.onnx
*.pt
*.pth
*.gguf

# Temporary files
tmp/
temp/