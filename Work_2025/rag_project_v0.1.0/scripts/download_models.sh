#!/bin/bash

# Make sure <PERSON><PERSON><PERSON> is running
echo "Checking if <PERSON><PERSON><PERSON> is running..."
docker compose -f docker/docker-compose.yaml up -d ollama

# Wait for <PERSON><PERSON><PERSON> to start
echo "Waiting for <PERSON><PERSON><PERSON> to start..."
sleep 10

# Pull the required models
echo "Pulling Llama3 8B model..."
docker compose -f docker/docker-compose.yaml exec ollama ollama pull llama3:8b

echo "Pulling Gemma 2B model (backup)..."
docker compose -f docker/docker-compose.yaml exec ollama ollama pull gemma:2b

echo "Models downloaded successfully!"