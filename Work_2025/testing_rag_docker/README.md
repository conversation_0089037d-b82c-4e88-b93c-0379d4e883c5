# RAG Testing Docker Application

This application provides a simple UI for testing RAG (Retrieval-Augmented Generation) with a strategic multi-query approach.

## Features

- Simple Streamlit UI for asking questions
- Document ingestion pipeline for PDFs and text files
- Strategic multi-query RAG agent for improved retrieval
- Docker containerization for easy deployment
- Chroma vector database for document storage

## Getting Started

### Prerequisites

- Docker and Docker Compose

#### Optional for GPU Acceleration
- NVIDIA GPU with CUDA support
- NVIDIA Container Toolkit installed

### Setup

1. Clone this repository
2. Create a `.env` file in the root directory (optional for custom settings)
3. Run the setup script to download required models:

```bash
chmod +x download_models.sh
./download_models.sh
```

4. Start the application:

```bash
docker compose up -d
```

5. Access the UI at http://localhost:8501

### Data Ingestion

You can ingest documents in two ways:

1. Through the UI: Upload files using the sidebar uploader
2. Using the ingestion script:

```bash
# Place your documents in the data/documents folder
mkdir -p data/documents
# Copy your documents to data/documents

# Run the ingestion script
docker compose exec rag_app python ingest.py
```

## Architecture

- **Ollama**: Provides the LLM and embedding models
- **Chroma**: Vector database for storing document embeddings (persisted to disk)
- **Streamlit**: Web UI for interacting with the RAG system

## Models Used

- LLM: Gemma 2B (works on both CPU and GPU)
- Embeddings: Gemma 2B

## RAG Strategy

This application uses a strategic multi-query RAG approach that:

1. Generates multiple search strategies for a given question
2. Executes searches with different query formulations
3. Combines and synthesizes the results for a comprehensive answer

### CPU vs. GPU Configuration

#### CPU-only Setup
The default configuration in docker-compose.yaml is set for CPU-only operation. No additional configuration is needed.

#### Enabling GPU Support
If you have an NVIDIA GPU and the NVIDIA Container Toolkit installed, you can enable GPU acceleration by uncommenting the GPU-related lines in the docker-compose.yaml file:

```yaml
services:
  ollama:
    # ... other configuration ...
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
```

This will allow Ollama to use your GPU for faster inference.
# Work_2025
