"""Main Streamlit application."""
import os
import sys
import streamlit as st
from typing import Dict, Any, List, Tuple
import yaml
import threading

# Add the project root to sys.path for the background script
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

# Start the S3 ingestion thread
try:
    from scripts.s3_ingest import start_ingestion_once

    print("Running one-time S3 ingestion on startup...")
    start_ingestion_once()
except Exception as e:
    print(f"Failed to run one-time S3 ingestion: {e}")

# Add the project root to the Python path to fix imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

# Use absolute imports instead of relative imports
from src.llm.ollama_client import OllamaClient
from src.rag.strategies import StrategicMultiQueryRAG
from src.document_processing.loader import DocumentLoader
from src.document_processing.splitter import DocumentSplitter
from src.rag.retriever import DocumentRetriever

# Set page config
st.set_page_config(page_title="Strategic RAG ", page_icon="🤖", layout="centered")

# Initialize components
@st.cache_resource
def initialize_components():
    """Initialize all components needed for the application."""
    # Print environment variables for debugging
    ollama_url = os.environ.get("OLLAMA_URL", "http://localhost:11434")
    if not ollama_url.startswith(('http://', 'https://')):
        ollama_url = f"http://{ollama_url}"
        os.environ["OLLAMA_URL"] = ollama_url
    
    st.write(f"Using OLLAMA_URL: {ollama_url}")
    
    # Load config
    config_path = "config/model_config.yaml"
    
    try:
        # Update config file with correct URL
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Update LLM base_url
        if 'llm' in config and 'base_url' in config['llm']:
            if not config['llm']['base_url'].startswith(('http://', 'https://')):
                config['llm']['base_url'] = f"http://{config['llm']['base_url']}"
        
        # Update embeddings base_url
        if 'embeddings' in config and 'base_url' in config['embeddings']:
            if not config['embeddings']['base_url'].startswith(('http://', 'https://')):
                config['embeddings']['base_url'] = f"http://{config['embeddings']['base_url']}"
        
        with open(config_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False)
    except Exception as e:
        st.error(f"Error updating config: {e}")
    
    # Initialize components
    try:
        ollama_client = OllamaClient.load_ollama_from_config(config_path)
        document_loader = DocumentLoader()
        document_splitter = DocumentSplitter()
        document_retriever = DocumentRetriever(config_path)
        rag_strategy = StrategicMultiQueryRAG(ollama_client, document_retriever)
        
        return {
            "ollama_client": ollama_client,
            "document_loader": document_loader,
            "document_splitter": document_splitter,
            "document_retriever": document_retriever,
            "rag_strategy": rag_strategy
        }
    except Exception as e:
        st.error(f"Error initializing components: {e}")
        raise e

# Initialize components
components = initialize_components()

# Main UI
st.title("rag_project_v0.1.0")

# Sidebar for data ingestion
with st.sidebar:
    st.header("Document Management")
    
    uploaded_file = st.file_uploader("Upload a document", type=["pdf", "txt"])
    
    if uploaded_file is not None:
        if st.button("Process Document"):
            with st.spinner("Processing document..."):
                # Save the uploaded file
                file_path = components["document_loader"].save_uploaded_file(uploaded_file)
                
                # Load the document
                documents = components["document_loader"].load_file(file_path)
                
                # Split documents
                chunks = components["document_splitter"].split_documents(documents)
                
                # Add to vector store
                components["document_retriever"].vector_store.add_documents(chunks)
                
                st.success(f"Added {len(chunks)} chunks to the vector store!")

# Main chat interface
if "messages" not in st.session_state:
    st.session_state.messages = [{"role": "assistant", "content": "Ask me a question about your documents!"}]

for message in st.session_state.messages:
    with st.chat_message(message["role"]):
        st.write(message["content"])

if prompt := st.chat_input("What would you like to know?"):
    st.session_state.messages.append({"role": "user", "content": prompt})
    with st.chat_message("user"):
        st.write(prompt)
    
    with st.chat_message("assistant"):
        with st.spinner("Thinking..."):
            # Process the query using strategic RAG
            result = components["rag_strategy"].query(prompt)
            response = result["final_answer"]
            
            st.write(response)
            with st.expander("View retrieval details"):
                # Display strategies
                st.subheader("Search Strategies")
                for strategy_type, search_terms in result["strategies"].items():
                    st.write(f"**{strategy_type}:** {search_terms}")
                
                # Display search results
                st.subheader("Retrieved Contexts")
                for strategy_type, search_result in result["search_results"].items():
                    st.write(f"**{strategy_type} SEARCH**")
                    st.write(f"Query: {search_result['query']}")
                    st.text(search_result['context'][:500] + "...")
    
    st.session_state.messages.append({"role": "assistant", "content": response})
