# RAG prompt templates
rag:
  base_template: |
    You are an AI Assistant that ONLY answers based on the provided context. 
    
    IMPORTANT RULES:
    1. NEVER make up information that is not explicitly stated in the context
    2. If the context doesn't contain the answer, say "I don't have enough information to answer this question"
    3. Do not use any prior knowledge about people, places, or events
    4. Stick strictly to the facts presented in the context
    5. Do not elaborate beyond what is directly supported by the context
    
    "context: {context} \n\n"
    "question: {question} \n\n"
    "Answer: (using ONLY facts from the context)

  strategy_generation: |
    For this question: "{question}"
    
    Generate 3 different search strategies to find comprehensive information:
    1. A broad search to understand the general concept
    2. A specific technical search for detailed mechanisms  
    3. A comparative search to understand relationships/differences
    
    Format as:
    BROAD: [search terms]
    TECHNICAL: [search terms]  
    COMPARATIVE: [search terms]

  synthesis: |
    Question: {question}
    
    I've gathered information using multiple search strategies:
    {combined_context}
    
    Provide a comprehensive answer that synthesizes information from all searches.

# Agentic Self-Reflective RAG templates
agentic_rag:
  initial_answer: |
    You are an AI assistant that provides accurate answers based on the given context.

    Context: {context}

    Question: {question}

    Provide a detailed answer based on the context. Be thorough but stick to the facts provided.

    Answer:

  self_reflection: |
    You are a critical AI reviewer. Analyze the following answer for accuracy, completeness, and potential issues.

    Original Question: {question}

    Answer to Review: {answer}

    Available Context: {context}

    Please evaluate this answer and identify:
    1. Any factual errors or inconsistencies
    2. Missing important information that should be included
    3. Areas where the answer could be more precise or detailed
    4. Whether the answer fully addresses the question

    Format your response as:
    ISSUES:
    - [List any problems you identify]

    IMPROVEMENTS:
    - [List specific suggestions for improvement]

    If the answer is satisfactory, write "No significant issues found."

  query_refinement: |
    Based on the reflection analysis, generate 2-3 refined search queries to gather additional information.

    Original Question: {original_question}

    Identified Issues:
    {issues}

    Suggested Improvements:
    {improvements}

    Generate specific search queries that would help address these issues and improvements:

    1. [First refined query]
    2. [Second refined query]
    3. [Third refined query]

  answer_improvement: |
    You are tasked with improving an answer based on additional context and reflection.

    Original Question: {question}

    Previous Answer: {original_answer}

    Enhanced Context (including additional information): {combined_context}

    Create an improved, more comprehensive answer that:
    1. Addresses any issues from the previous answer
    2. Incorporates relevant new information from the enhanced context
    3. Provides a more complete and accurate response
    4. Maintains clarity and coherence

    Improved Answer: