"""Ollama LLM client implementation."""
import os
from typing import Dict, Any, List, Optional
import yaml

from langchain_ollama import ChatOllama, OllamaEmbeddings
from langchain_core.output_parsers import StrOutputParser

from .base import BaseLLM

class OllamaClient(BaseLLM):
    """Client for Ollama LLM service."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Ollama client with configuration."""
        self.config = config
        base_url = config.get('base_url', os.environ.get("OLLAMA_URL", "http://localhost:11434"))
        
        # Ensure base_url has a protocol
        if not base_url.startswith(('http://', 'https://')):
            base_url = f"http://{base_url}"
            
        self.base_url = base_url
        self.model_name = os.environ.get("OLLAMA_MODEL") or config.get('models', {}).get('default', "gemma:2b")
        self.temperature = config.get('parameters', {}).get('temperature', 0.5)
        self.max_tokens = config.get('parameters', {}).get('max_tokens', 500)
        
        # Print for debugging
        print(f"OllamaClient initialized with base_url: {self.base_url}")
        print(f"🔧 Using model: {self.model_name}")

        self.llm = ChatOllama(
            base_url=self.base_url,
            model=self.model_name,
            temperature=self.temperature,
            max_tokens=self.max_tokens
        )
        
        self.chain = self.llm | StrOutputParser()
    
    def generate(self, prompt: str, **kwargs) -> str:
        """Generate text from a prompt."""
        return self.chain.invoke(prompt)
    
    def embed(self, text: str) -> List[float]:
        """Generate embeddings for text."""
        embedding_model = OllamaEmbeddings(
            base_url=self.base_url,
            model=self.model_name,
        )
        return embedding_model.embed_query(text)
    
    @classmethod
    def load_ollama_from_config(cls, config_path: str = "config/model_config.yaml") -> 'OllamaClient':
        """Load Ollama client from configuration file."""
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            llm_config = config.get('llm', {})
            
            # Ensure base_url has protocol
            base_url = llm_config.get('base_url', os.environ.get("OLLAMA_URL", "http://localhost:11434"))
            if not base_url.startswith(('http://', 'https://')):
                base_url = f"http://{base_url}"
                llm_config['base_url'] = base_url
            
            print(f"Loading OllamaClient with base_url: {base_url}")
            return cls(llm_config)
        except Exception as e:
            print(f"Error loading OllamaClient from config: {e}")
            # Fallback to default configuration
            default_config = {
                'base_url': 'http://localhost:11434',
                'models': {'default': 'gemma:2b'},
                'parameters': {'temperature': 0.5, 'max_tokens': 500}
            }
            return cls(default_config)
