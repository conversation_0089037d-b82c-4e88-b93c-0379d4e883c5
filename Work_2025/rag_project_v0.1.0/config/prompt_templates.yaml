# RAG prompt templates
rag:
  base_template: |
    You are an AI Assistant that ONLY answers based on the provided context. 
    
    IMPORTANT RULES:
    1. NEVER make up information that is not explicitly stated in the context
    2. If the context doesn't contain the answer, say "I don't have enough information to answer this question"
    3. Do not use any prior knowledge about people, places, or events
    4. Stick strictly to the facts presented in the context
    5. Do not elaborate beyond what is directly supported by the context
    
    "context: {context} \n\n"
    "question: {question} \n\n"
    "Answer: (using ONLY facts from the context)

  strategy_generation: |
    For this question: "{question}"
    
    Generate 3 different search strategies to find comprehensive information:
    1. A broad search to understand the general concept
    2. A specific technical search for detailed mechanisms  
    3. A comparative search to understand relationships/differences
    
    Format as:
    BROAD: [search terms]
    TECHNICAL: [search terms]  
    COMPARATIVE: [search terms]

  synthesis: |
    Question: {question}
    
    I've gathered information using multiple search strategies:
    {combined_context}
    
    Provide a comprehensive answer that synthesizes information from all searches.