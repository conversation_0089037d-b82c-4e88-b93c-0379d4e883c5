{"cells": [{"cell_type": "markdown", "id": "f161dcc3", "metadata": {}, "source": ["## Local RAG Trial\n"]}, {"cell_type": "markdown", "id": "4a84c5a5", "metadata": {}, "source": ["## Libraries and Environemnt\n"]}, {"cell_type": "code", "execution_count": 6, "id": "401ccc9a", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["#Load environment variables FIRST\n", "import os\n", "from dotenv import load_dotenv\n", "\n", "# Load environment variables before any other imports\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": 7, "id": "28aba08c", "metadata": {}, "outputs": [], "source": ["from langchain_ollama import ChatOllama, OllamaEmbeddings\n", "from langchain_chroma import Chroma\n", "from langchain_community.document_loaders import PyPDFLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain.prompts import ChatPromptTemplate\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langsmith import traceable\n"]}, {"cell_type": "markdown", "id": "6f37e876", "metadata": {}, "source": ["## Local LLM and embedding models"]}, {"cell_type": "code", "execution_count": 8, "id": "47d277ba", "metadata": {}, "outputs": [], "source": ["def initialize_models():\n", "    llm = ChatOllama(\n", "        base_url=\"http://localhost:11434\",\n", "        model = \"gemma:2b\",\n", "        temperature=0.5,\n", "        max_tokens = 500\n", "    )\n", "\n", "    embedding_model = OllamaEmbeddings(\n", "        base_url=\"http://localhost:11434\",\n", "        model = \"gemma:2b\",\n", "    )\n", "    return llm, embedding_model\n", "llm, embedding_model = initialize_models()"]}, {"cell_type": "markdown", "id": "c78cb677", "metadata": {}, "source": ["## Data Preprocessing\n"]}, {"cell_type": "code", "execution_count": 9, "id": "03d4547d", "metadata": {}, "outputs": [], "source": ["\n", "@traceable\n", "def load_pdf(pdf):\n", "    loader = PyPDFLoader(pdf)\n", "    documents.extend(loader.load())\n", "\n", "@traceable\n", "def split_and_store(documents):\n", "    text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)\n", "    chunks = text_splitter.split_documents(documents)\n", "    vector_store = Chroma.from_documents(chunks, embedding_model, persist_directory=\"./chroma_db_gemma\")\n", "    return vector_store\n", "\n", "\n", "pdf1 = \"./attention.pdf\"\n", "\n", "pdfFiles = [pdf1]\n", "\n", "documents = []\n", "for pdf in pdfFiles:\n", "    load_pdf(pdf)\n", "\n", "vector_store = split_and_store(documents)\n", "\n"]}, {"cell_type": "markdown", "id": "5251b659", "metadata": {}, "source": ["## Retrieval"]}, {"cell_type": "code", "execution_count": 10, "id": "9fa4d33f", "metadata": {}, "outputs": [], "source": ["@traceable\n", "def get_relevant_docs(query, k=1):\n", "    docs = vector_store.similarity_search_with_score(query, k=k)\n", "    context_with_scores = []\n", "    for doc, score in docs:\n", "        context_with_scores.append(f\"[Score: {score:.4f}]\\n{doc.page_content}\")\n", "    \n", "    # Join all chunks with their scores\n", "    context = \"\\n\\n\" + \"\\n\\n---\\n\\n\".join(context_with_scores)\n", "    return context\n", "\n", "@traceable\n", "def ask_question(question,chain):\n", "    context = get_relevant_docs(question)\n", "    print(\"CONTEXT:\")\n", "    print(context)\n", "    response = chain.invoke({\"context\": context, \"question\": question})\n", "    return response\n", "#retriever = vector_store.as_retriever(search_type =\"similarity\", search_kwargs={\"k\":1 })\n", "\n", "#print(retriever.get_relevant_documents(\"What is the attention mechanism?\"))\n", "#retrieved_docs = retriever.get_relevant_documents(\"What is multi-head attention?\")\n", "#context = \"\\n\\n\".join([d.page_content for d in retrieved_docs])\n", "#print(context)\n"]}, {"cell_type": "code", "execution_count": 11, "id": "acd70ef5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CONTEXT:\n", "\n", "\n", "[Score: 0.4343]\n", "sequential nature precludes parallelization within training examples, which becomes critical at longer\n", "sequence lengths, as memory constraints limit batching across examples. Recent work has achieved\n", "significant improvements in computational efficiency through factorization tricks [21] and conditional\n", "computation [32], while also improving model performance in case of the latter. The fundamental\n", "constraint of sequential computation, however, remains.\n", "Attention mechanisms have become an integral part of compelling sequence modeling and transduc-\n", "tion models in various tasks, allowing modeling of dependencies without regard to their distance in\n", "the input or output sequences [2, 19]. In all but a few cases [27], however, such attention mechanisms\n", "are used in conjunction with a recurrent network.\n", "In this work we propose the Transformer, a model architecture eschewing recurrence and instead\n", "relying entirely on an attention mechanism to draw global dependencies between input and output.\n", "ASNWER:\n", "The passage does not provide information about how the Transformer reduces the number of operations needed to relate distant positions compared to ConvS2S and ByteNet, so I cannot answer this question from the provided context.\n"]}], "source": ["prompt_template = ChatPromptTemplate.from_template(\n", "    \"\"\"\n", "    You are an AI Assistant that ONLY answers based on the provided context. \n", "    \n", "    IMPORTANT RULES:\n", "    1. NEVER make up information that is not explicitly stated in the context\n", "    2. If the context doesn't contain the answer, say \"I don't have enough information to answer this question\"\n", "    3. Do not use any prior knowledge about people, places, or events\n", "    4. Stick strictly to the facts presented in the context\n", "    5. Do not elaborate beyond what is directly supported by the context\n", "    \n", "    Also, summarize the response in MD format\n", "    \n", "    \"context: {context} \\n\\n\"\n", "    \"question: {question} \\n\\n\"\n", "    \"Answer: (using ONLY facts from the context)\n", "    \n", "    \"\"\"\n", ")\n", "\n", "chain = prompt_template | llm | StrOutputParser()\n", "\n", "question = \"How does the Transformer reduce the number of operations needed to relate distant positions compared to ConvS2S and ByteNet?\"\n", "#How does the Transformer reduce the number of operations needed to relate distant positions compared to ConvS2S and ByteNet?\n", "\n", "\n", "response = ask_question(question, chain)\n", "print(\"ASNWER:\")\n", "print(response)\n"]}, {"cell_type": "markdown", "id": "ac14957d", "metadata": {}, "source": ["## Different style of AGENTS"]}, {"cell_type": "markdown", "id": "86a9b7dd", "metadata": {}, "source": ["multi-step reasoning agent. <PERSON><PERSON><PERSON> THINK OF PARRALLELIZATION OF MULTIPLE SEARCHES AT THE SAME TIME AND COMBINE RESULTS, LIKE THINK OF maybe the search strategy we can do something like this. I added it below and it doesnt iterate so different kind of agent?. "]}, {"cell_type": "code", "execution_count": 12, "id": "6f1c321e", "metadata": {}, "outputs": [], "source": ["\n", "\n", "@traceable\n", "def agentic_rag(question: str, max_iterations: int = 3):\n", "    \"\"\"\n", "    Agentic RAG that can perform multi-step reasoning and refinement\n", "    \"\"\"\n", "    conversation_history = []\n", "    current_question = question\n", "    \n", "    for iteration in range(max_iterations):\n", "        print(f\"\\n--- Iteration {iteration + 1} ---\")\n", "        \n", "        # Step 1: Analyze the question and decide on search strategy\n", "        analysis_prompt = f\"\"\"\n", "        Analyze this question and determine the best search strategy:\n", "        Question: {current_question}\n", "        \n", "        Previous context: {conversation_history}\n", "        \n", "        What specific terms or concepts should I search for? \n", "        Should I break this into sub-questions?\n", "        Provide 2-3 specific search terms or phrases that would find the most relevant information.\n", "        Format your response as: SEARCH_TERMS: term1, term2, term3\n", "        \"\"\"\n", "        \n", "        search_strategy = llm.invoke(analysis_prompt).content\n", "        print(f\"Search Strategy: {search_strategy}\")\n", "        if \"SEARCH_TERMS:\" in search_strategy:\n", "            search_terms = search_strategy.split(\"SEARCH_TERMS:\")[1].strip()\n", "            search_queries = [term.strip() for term in search_terms.split(\",\")]\n", "        else:\n", "            # Fallback to original question if parsing fails\n", "            search_queries = [current_question]\n", "        \n", "        # Step 2: Search for information\n", "        print(f\"Executing searches for: {search_queries}\")\n", "        \n", "        # Step 3: Execute the search strategy\n", "        all_contexts = []\n", "        for search_query in search_queries:\n", "            context = get_relevant_docs(search_query, k=2)  # Fewer docs per query\n", "            all_contexts.append(f\"Search: '{search_query}'\\nResults:\\n{context}\")\n", "        \n", "        combined_context = \"\\n\\n---\\n\\n\".join(all_contexts)\n", "        \n", "        # Step 3: Evaluate if we have enough information\n", "        evaluation_prompt = f\"\"\"\n", "        Question: {current_question}\n", "        Retrieved Context: {combined_context}\n", "        \n", "        Do I have enough information to answer this question completely? \n", "        If not, what additional information do I need?\n", "        Respond with either \"SUFFICIENT\" or \"NEED_MORE: [specific information needed]\"\n", "        \"\"\"\n", "        \n", "        evaluation = llm.invoke(evaluation_prompt).content\n", "        print(f\"Evaluation: {evaluation}\")\n", "        \n", "        if evaluation.startswith(\"SUFFICIENT\"):\n", "            # Generate final answer\n", "            final_answer = chain.invoke({\"context\": context, \"question\": current_question})\n", "            return final_answer\n", "        elif evaluation.startswith(\"NEED_MORE\"):\n", "            # Extract what we need and refine the search\n", "            needed_info = evaluation.split(\"NEED_MORE:\")[1].strip()\n", "            current_question = f\"{current_question} Specifically: {needed_info}\"\n", "            conversation_history.append(f\"Iteration {iteration + 1}: {context[:200]}...\")\n", "        \n", "    # If we've exhausted iterations, provide best answer available\n", "    final_context = get_relevant_docs(question, k=7)\n", "    return chain.invoke({\"context\": final_context, \"question\": question})"]}, {"cell_type": "code", "execution_count": 13, "id": "c7ba8e58", "metadata": {}, "outputs": [], "source": ["@traceable\n", "def strategic_multi_query_rag(question: str):\n", "    \"\"\"\n", "    Agent that generates multiple strategic search queries and combines results\n", "    \"\"\"\n", "    \n", "    # Step 1: Generate multiple search strategies\n", "    strategy_prompt = f\"\"\"\n", "    For this question: \"{question}\"\n", "    \n", "    Generate 3 different search strategies to find comprehensive information if you deem the strategy necessary (if you think no technical strategy is needed just put N/A):\n", "    1. A broad search to understand the general concept\n", "    2. A specific technical search for detailed mechanisms  \n", "    3. A comparative search to understand relationships/differences\n", "    \n", "    Format as:\n", "    BROAD: [search terms]\n", "    TECHNICAL: [search terms]  \n", "    COMPARATIVE: [search terms]\n", "    \"\"\"\n", "    \n", "    strategies = llm.invoke(strategy_prompt).content\n", "    #print(f\"Search Strategies:\\n{strategies}\")\n", "    \n", "    # Step 2: Extract and execute each strategy\n", "    search_results = {}\n", "    \n", "    for strategy_type in [\"BROAD\", \"TECHNICAL\", \"COMPARATIVE\"]:\n", "        if f\"{strategy_type}:\" in strategies:\n", "            search_terms = strategies.split(f\"{strategy_type}:\")[1].split(\"\\n\")[0].strip()\n", "            #print(f\"\\nExecuting {strategy_type} search: {search_terms}\")\n", "            \n", "            context = get_relevant_docs(search_terms, k=3)\n", "            search_results[strategy_type] = {\n", "                \"query\": search_terms,\n", "                \"context\": context\n", "            }\n", "    \n", "    # Step 3: Synthesize all search results\n", "    combined_context = \"\"\n", "    for strategy_type, result in search_results.items():\n", "        combined_context += f\"\\n\\n=== {strategy_type} SEARCH ===\\n\"\n", "        combined_context += f\"Query: {result['query']}\\n\"\n", "        combined_context += f\"Results: {result['context']}\\n\"\n", "    \n", "    # Step 4: Generate comprehensive answer\n", "    synthesis_prompt = f\"\"\"\n", "    Question: {question}\n", "    \n", "    I've gathered information using multiple search strategies:\n", "    {combined_context}\n", "    \n", "    Provide a comprehensive answer that synthesizes information from all searches.\n", "    \"\"\"\n", "    \n", "    final_answer = llm.invoke(synthesis_prompt).content\n", "    return final_answer"]}, {"cell_type": "markdown", "id": "c4184dfc", "metadata": {}, "source": ["self-reflective"]}, {"cell_type": "code", "execution_count": 14, "id": "a51eca53", "metadata": {}, "outputs": [], "source": ["@traceable\n", "def self_reflective_rag(question: str):\n", "    \"\"\"\n", "    RAG agent that reflects on its own answers and improves them\n", "    \"\"\"\n", "    \n", "    # Initial answer\n", "    initial_context = get_relevant_docs(question, k=3)\n", "    initial_answer = chain.invoke({\"context\": initial_context, \"question\": question})\n", "    \n", "    # Self-reflection\n", "    reflection_prompt = f\"\"\"\n", "    I just answered this question: {question}\n", "    My answer was: {initial_answer}\n", "    Based on context: {initial_context[:300]}...\n", "    \n", "    Critically evaluate my answer:\n", "    1. Is it complete and accurate?\n", "    2. What important aspects might be missing?\n", "    3. Should I search for additional information?\n", "    4. Rate the answer quality (1-10) and explain why.\n", "    \n", "    Provide specific suggestions for improvement.\n", "    \"\"\"\n", "    \n", "    reflection = llm.invoke(reflection_prompt).content\n", "    print(f\"Self-Reflection: {reflection}\")\n", "    \n", "    # If reflection suggests improvement, search for more info\n", "    if \"search for additional\" in reflection.lower() or any(word in reflection.lower() for word in [\"incomplete\", \"missing\", \"need more\"]):\n", "        # Extract key terms for additional search\n", "        additional_search_prompt = f\"\"\"\n", "        Based on this reflection: {reflection}\n", "        What specific terms should I search for to improve my answer to: {question}\n", "        Provide 2-3 specific search terms.\n", "        \"\"\"\n", "        \n", "        search_terms = llm.invoke(additional_search_prompt).content\n", "        print(f\"Additional search terms: {search_terms}\")\n", "        \n", "        # Search with new terms\n", "        additional_context = get_relevant_docs(search_terms, k=3)\n", "        \n", "        # Generate improved answer\n", "        combined_context = initial_context + \"\\n\\n--- Additional Information ---\\n\\n\" + additional_context\n", "        improved_answer = chain.invoke({\"context\": combined_context, \"question\": question})\n", "        \n", "        return {\n", "            \"initial_answer\": initial_answer,\n", "            \"reflection\": reflection,\n", "            \"improved_answer\": improved_answer,\n", "            \"final_answer\": improved_answer\n", "        }\n", "    \n", "    return {\n", "        \"initial_answer\": initial_answer,\n", "        \"reflection\": reflection,\n", "        \"final_answer\": initial_answer\n", "    }"]}, {"cell_type": "markdown", "id": "5205fb0b", "metadata": {}, "source": ["question decompostion agent"]}, {"cell_type": "code", "execution_count": 15, "id": "e9263706", "metadata": {}, "outputs": [], "source": ["@traceable\n", "def decomposition_agent(complex_question: str):\n", "    \"\"\"\n", "    Agent that breaks down complex questions into simpler sub-questions\n", "    \"\"\"\n", "    \n", "    # Decompose the question\n", "    decomposition_prompt = f\"\"\"\n", "    Break down this complex question into 2-4 simpler sub-questions that, when answered together, \n", "    would fully address the original question:\n", "    \n", "    Complex Question: {complex_question}\n", "    \n", "    Provide sub-questions as a numbered list.\n", "    \"\"\"\n", "    \n", "    decomposition = llm.invoke(decomposition_prompt).content\n", "    print(f\"Question Decomposition:\\n{decomposition}\")\n", "    \n", "    # Extract sub-questions (simple parsing)\n", "    sub_questions = []\n", "    for line in decomposition.split('\\n'):\n", "        if line.strip() and (line.strip()[0].isdigit() or line.strip().startswith('-')):\n", "            sub_questions.append(line.strip().split('.', 1)[-1].strip())\n", "    \n", "    # Answer each sub-question\n", "    sub_answers = {}\n", "    for i, sub_q in enumerate(sub_questions, 1):\n", "        print(f\"\\n--- Answering Sub-question {i}: {sub_q} ---\")\n", "        context = get_relevant_docs(sub_q, k=3)\n", "        answer = chain.invoke({\"context\": context, \"question\": sub_q})\n", "        sub_answers[sub_q] = answer\n", "        print(f\"Answer: {answer}\")\n", "    \n", "    # Synthesize final answer\n", "    synthesis_prompt = f\"\"\"\n", "    Original complex question: {complex_question}\n", "    \n", "    Sub-questions and their answers:\n", "    {chr(10).join([f\"Q: {q}A: {a}\" for q, a in sub_answers.items()])}\n", "    \n", "    Now synthesize these sub-answers into a comprehensive response to the original question.\"\"\"\n", "    \n", "    final_answer = llm.invoke(synthesis_prompt).content\n", "    \n", "    return {\n", "        \"original_question\": complex_question,\n", "        \"sub_questions\": sub_questions,\n", "        \"sub_answers\": sub_answers,\n", "        \"final_answer\": final_answer\n", "    }"]}, {"cell_type": "markdown", "id": "0704973e", "metadata": {}, "source": ["Agent Comparison:"]}, {"cell_type": "code", "execution_count": 16, "id": "76c79662", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Multi-Step Reasoning Agent ===\n", "\n", "--- Iteration 1 ---\n", "Search Strategy: SEARCH_TERMS: multi-head attention, single attention, computational advantages\n", "Executing searches for: ['multi-head attention', 'single attention', 'computational advantages']\n", "Evaluation: SUFFICIENT\n", "\n", "To fully answer this question, I would need additional information about:\n", "\n", "- The specific details of the computational advantages of multi-head attention compared to single attention.\n", "- The computational complexity of the Transformer model and how it compares to other sequence models in terms of computational efficiency.\n", "- How the computational advantages of the Transformer model translate into improved performance on various sequence modeling tasks.\n", "- The limitations or potential drawbacks of the Transformer model, such as its dependence on attention mechanisms and the potential for increased computational cost.\n", "Final Answer: The context does not provide any information about multi-head attention, so I cannot answer this question from the provided context.\n", "\n", "=== Strategic Multi-Query ===\n", "Final Answer: Sure, here's a comprehensive answer to the question:\n", "\n", "Multi-head attention is a technique that has been shown to improve upon single attention mechanisms in natural language processing (NLP). \n", "\n", "**Key advantages of multi-head attention:**\n", "\n", "* **Reduced variance:** By averaging over multiple attention heads, multi-head attention reduces the variance of the model. This can help to improve the robustness of the model to noise and outliers in the data.\n", "* **Increased capacity:** Multi-head attention can also increase the capacity of the model by allowing it to learn from different aspects of the input text. This can lead to improved performance on tasks such as sentiment analysis and text classification.\n", "* **Enhanced interpretability:** Multi-head attention can also enhance the interpretability of the model by allowing it to learn different representations of the input text. This can be useful for debugging and understanding the model's behavior.\n", "\n", "**Computational advantages of multi-head attention:**\n", "\n", "* **Increased computational cost:** Multi-head attention can be computationally more expensive than single attention, as it requires the model to perform multiple attention operations on the input text.\n", "* **Increased memory requirements:** The model also requires more memory to store the multiple attention weights, which can be a problem for models with limited memory resources.\n", "* **Computational complexity:** The computational complexity of multi-head attention is O(N * d), where N is the length of the input text and d is the number of attention heads. This can be a problem for long input texts or a large number of attention heads.\n", "\n", "Overall, multi-head attention is a powerful technique that can improve the performance of NLP models. However, it is important to be aware of the computational advantages and limitations of this technique before using it in a real-world application.\n", "\n", "=== Self-Reflective Agent ===\n", "Self-Reflection: **1. Is it complete and accurate?**\n", "No, the answer is not complete and accurate. It does not provide a clear definition of multi-head attention and does not discuss its computational advantages in detail.\n", "\n", "**2. What important aspects might be missing?**\n", "The answer could benefit from a more thorough explanation of the following concepts:\n", "\n", "- What is multi-head attention?\n", "- How does multi-head attention improve upon single attention mechanisms?\n", "- What are the computational advantages of multi-head attention?\n", "\n", "**3. Should I search for additional information?**\n", "Yes, searching for additional information would be beneficial. This could include reading articles or papers on multi-head attention, as well as tutorials or lectures on computational efficiency.\n", "\n", "**4. Quality rating and explanation:**\n", "Quality rating: 6/10\n", "The answer provides a basic overview of multi-head attention, but it could be improved by providing more context and by discussing its computational advantages in more detail.\n", "\n", "**Suggestions for improvement:**\n", "\n", "- Provide a more comprehensive definition of multi-head attention.\n", "- Discuss the computational advantages of multi-head attention in more detail, including the use of attention weights and the parallel processing capability.\n", "- Include a comparison with single attention mechanisms in terms of computational efficiency.\n", "- Provide additional relevant information and resources for further understanding.\n", "Additional search terms: **Search Terms:**\n", "\n", "1. Multi-head attention definition\n", "2. Computational advantages of multi-head attention\n", "3. Comparison of single and multi-head attention\n", "\n", "=== Question Decomposition Agent ===\n", "Question Decomposition:\n", "**Sub-questions:**\n", "\n", "1. How does multi-head attention differ from single attention in terms of its architecture and attention weights?\n", "\n", "\n", "2. How does multi-head attention address the issue of attention weights being concentrated on specific parts of the input?\n", "\n", "\n", "3. How does multi-head attention leverage the computational efficiency of multi-head architecture compared to single attention?\n", "\n", "\n", "4. What are the computational advantages of multi-head attention over single attention in terms of memory usage and query/key processing speed?\n", "\n", "--- Answering Sub-question 1: How does multi-head attention differ from single attention in terms of its architecture and attention weights? ---\n", "Answer: The context does not provide information about multi-head attention, so I cannot answer this question from the provided context.\n", "\n", "--- Answering Sub-question 2: How does multi-head attention address the issue of attention weights being concentrated on specific parts of the input? ---\n", "Answer: The context does not provide information about multi-head attention, so I cannot answer this question from the provided context.\n", "\n", "--- Answering Sub-question 3: How does multi-head attention leverage the computational efficiency of multi-head architecture compared to single attention? ---\n", "Answer: The context does not provide information about the multi-head attention mechanism, so I cannot answer this question from the provided context.\n", "\n", "--- Answering Sub-question 4: What are the computational advantages of multi-head attention over single attention in terms of memory usage and query/key processing speed? ---\n", "Answer: The context does not provide any information about the computational advantages of multi-head attention over single attention in terms of memory usage and query/key processing speed. Therefore, I cannot answer this question from the provided context.\n", "Complete decomposition result:\n", "\n", "ORIGINAL_QUESTION:\n", "How does multi-head attention improve upon single attention mechanisms and what are its computational advantages?\n", "\n", "SUB_QUESTIONS:\n", "['How does multi-head attention differ from single attention in terms of its architecture and attention weights?', 'How does multi-head attention address the issue of attention weights being concentrated on specific parts of the input?', 'How does multi-head attention leverage the computational efficiency of multi-head architecture compared to single attention?', 'What are the computational advantages of multi-head attention over single attention in terms of memory usage and query/key processing speed?']\n", "\n", "SUB_ANSWERS:\n", "{'How does multi-head attention differ from single attention in terms of its architecture and attention weights?': 'The context does not provide information about multi-head attention, so I cannot answer this question from the provided context.', 'How does multi-head attention address the issue of attention weights being concentrated on specific parts of the input?': 'The context does not provide information about multi-head attention, so I cannot answer this question from the provided context.', 'How does multi-head attention leverage the computational efficiency of multi-head architecture compared to single attention?': 'The context does not provide information about the multi-head attention mechanism, so I cannot answer this question from the provided context.', 'What are the computational advantages of multi-head attention over single attention in terms of memory usage and query/key processing speed?': 'The context does not provide any information about the computational advantages of multi-head attention over single attention in terms of memory usage and query/key processing speed. Therefore, I cannot answer this question from the provided context.'}\n", "\n", "FINAL_ANSWER:\n", "Sure, here's a comprehensive response to the original question:\n", "\n", "**How does multi-head attention differ from single attention in terms of its architecture and attention weights?**\n", "\n", "Multi-head attention is a variant of single attention that incorporates multiple attention layers within a single model. Each attention layer focuses on different aspects of the input using different sets of weights, allowing the model to capture a more comprehensive representation of the input.\n", "\n", "**How does multi-head attention address the issue of attention weights being concentrated on specific parts of the input?**\n", "\n", "Multi-head attention addresses the issue of attention weights being concentrated on specific parts of the input by using different sets of weights for each attention layer. This helps to ensure that the model focuses on different aspects of the input and reduces the risk of overfitting.\n", "\n", "**How does multi-head attention leverage the computational efficiency of multi-head architecture compared to single attention?**\n", "\n", "Multi-head attention can leverage the computational efficiency of the multi-head architecture by using a parallel architecture. This allows the model to perform multiple attention operations in parallel, reducing the time required for each attention layer. Additionally, the use of attention weights allows the model to focus on different parts of the input, further reducing computational costs.\n", "\n", "**What are the computational advantages of multi-head attention over single attention in terms of memory usage and query/key processing speed?**\n", "\n", "The context does not provide any information about the computational advantages of multi-head attention over single attention in terms of memory usage and query/key processing speed. Therefore, I cannot answer this question from the provided context.\n"]}], "source": ["# Test the agentic approaches\n", "question = \"How does multi-head attention improve upon single attention mechanisms and what are its computational advantages?\"\n", "\n", "# Try different agentic approaches\n", "print(\"=== Multi-Step Reasoning Agent ===\")\n", "result1 = agentic_rag(question)\n", "print(f\"Final Answer: {result1}\")\n", "\n", "print(\"\\n=== Strategic Multi-Query ===\")\n", "result5 = strategic_multi_query_rag(question)\n", "print(f\"Final Answer: {result5}\")\n", "\n", "print(\"\\n=== Self-Reflective Agent ===\")\n", "result2 = self_reflective_rag(question)\n", "\n", "print(\"\\n=== Question Decomposition Agent ===\")\n", "result3 = decomposition_agent(question)\n", "print(\"Complete decomposition result:\")\n", "for key, value in result3.items():\n", "    print(f\"\\n{key.upper()}:\")\n", "    print(value)\n"]}, {"cell_type": "markdown", "id": "bf241da7", "metadata": {}, "source": ["## Evaluation"]}, {"cell_type": "code", "execution_count": 17, "id": "ef9991e6", "metadata": {}, "outputs": [{"ename": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evalue": "Error tokenizing data. C error: EOF inside string starting at row 10", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                               <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[17], line 6\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mlangchain_core\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdocuments\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Document\n\u001b[1;32m      5\u001b[0m \u001b[38;5;66;03m# Load your dataset\u001b[39;00m\n\u001b[0;32m----> 6\u001b[0m df \u001b[38;5;241m=\u001b[39m \u001b[43mpd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_csv\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m./story_dataset.csv\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m      8\u001b[0m \u001b[38;5;66;03m# Convert dataframe rows to documents\u001b[39;00m\n\u001b[1;32m      9\u001b[0m documents \u001b[38;5;241m=\u001b[39m []\n", "File \u001b[0;32m~/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/readers.py:1026\u001b[0m, in \u001b[0;36mread_csv\u001b[0;34m(filepath_or_buffer, sep, delimiter, header, names, index_col, usecols, dtype, engine, converters, true_values, false_values, skipinitialspace, skiprows, skipfooter, nrows, na_values, keep_default_na, na_filter, verbose, skip_blank_lines, parse_dates, infer_datetime_format, keep_date_col, date_parser, date_format, dayfirst, cache_dates, iterator, chunksize, compression, thousands, decimal, lineterminator, quotechar, quoting, doublequote, escapechar, comment, encoding, encoding_errors, dialect, on_bad_lines, delim_whitespace, low_memory, memory_map, float_precision, storage_options, dtype_backend)\u001b[0m\n\u001b[1;32m   1013\u001b[0m kwds_defaults \u001b[38;5;241m=\u001b[39m _refine_defaults_read(\n\u001b[1;32m   1014\u001b[0m     dialect,\n\u001b[1;32m   1015\u001b[0m     delimiter,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1022\u001b[0m     dtype_backend\u001b[38;5;241m=\u001b[39mdtype_backend,\n\u001b[1;32m   1023\u001b[0m )\n\u001b[1;32m   1024\u001b[0m kwds\u001b[38;5;241m.\u001b[39mupdate(kwds_defaults)\n\u001b[0;32m-> 1026\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_read\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilepath_or_buffer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwds\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/readers.py:626\u001b[0m, in \u001b[0;36m_read\u001b[0;34m(filepath_or_buffer, kwds)\u001b[0m\n\u001b[1;32m    623\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m parser\n\u001b[1;32m    625\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m parser:\n\u001b[0;32m--> 626\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mparser\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnrows\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/readers.py:1923\u001b[0m, in \u001b[0;36mTextFileReader.read\u001b[0;34m(self, nrows)\u001b[0m\n\u001b[1;32m   1916\u001b[0m nrows \u001b[38;5;241m=\u001b[39m validate_integer(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnrows\u001b[39m\u001b[38;5;124m\"\u001b[39m, nrows)\n\u001b[1;32m   1917\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1918\u001b[0m     \u001b[38;5;66;03m# error: \"ParserBase\" has no attribute \"read\"\u001b[39;00m\n\u001b[1;32m   1919\u001b[0m     (\n\u001b[1;32m   1920\u001b[0m         index,\n\u001b[1;32m   1921\u001b[0m         columns,\n\u001b[1;32m   1922\u001b[0m         col_dict,\n\u001b[0;32m-> 1923\u001b[0m     ) \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_engine\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# type: ignore[attr-defined]\u001b[39;49;00m\n\u001b[1;32m   1924\u001b[0m \u001b[43m        \u001b[49m\u001b[43mnrows\u001b[49m\n\u001b[1;32m   1925\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1926\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:\n\u001b[1;32m   1927\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mclose()\n", "File \u001b[0;32m~/Library/Python/3.9/lib/python/site-packages/pandas/io/parsers/c_parser_wrapper.py:234\u001b[0m, in \u001b[0;36mCParserWrapper.read\u001b[0;34m(self, nrows)\u001b[0m\n\u001b[1;32m    232\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    233\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlow_memory:\n\u001b[0;32m--> 234\u001b[0m         chunks \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_reader\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_low_memory\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnrows\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    235\u001b[0m         \u001b[38;5;66;03m# destructive to chunks\u001b[39;00m\n\u001b[1;32m    236\u001b[0m         data \u001b[38;5;241m=\u001b[39m _concatenate_chunks(chunks)\n", "File \u001b[0;32mparsers.pyx:838\u001b[0m, in \u001b[0;36mpandas._libs.parsers.TextReader.read_low_memory\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mparsers.pyx:905\u001b[0m, in \u001b[0;36mpandas._libs.parsers.TextReader._read_rows\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mparsers.pyx:874\u001b[0m, in \u001b[0;36mpandas._libs.parsers.TextReader._tokenize_rows\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mparsers.pyx:891\u001b[0m, in \u001b[0;36mpandas._libs.parsers.TextReader._check_tokenize_status\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mparsers.pyx:2061\u001b[0m, in \u001b[0;36mpandas._libs.parsers.raise_parser_error\u001b[0;34m()\u001b[0m\n", "\u001b[0;31mParserError\u001b[0m: Error tokenizing data. C error: EOF inside string starting at row 10"]}], "source": ["# Load the dataset\n", "import pandas as pd\n", "from langchain_core.documents import Document\n", "\n", "# Load your dataset\n", "df = pd.read_csv('./story_dataset.csv')\n", "\n", "# Convert dataframe rows to documents\n", "documents = []\n", "for i, row in df.iterrows():\n", "    # Create a document with the query and answer\n", "    doc = Document(\n", "        page_content=f\"Question: {row['query']}\\nAnswer: {row['answer']}\",\n", "        metadata={\n", "            \"source\": \"dataset.csv\",\n", "            \"row_id\": i,\n", "            \"type\": \"qa_pair\"\n", "        }\n", "    )\n", "    documents.append(doc)\n", "\n", "print(f\"Created {len(documents)} documents from storay_dataset.csv\")\n", "\n", "# Add documents to the vector store\n", "@traceable\n", "def add_documents_to_vectorstore(docs):\n", "    text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)\n", "    chunks = text_splitter.split_documents(docs)\n", "    print(f\"Split into {len(chunks)} chunks\")\n", "    \n", "    # Add to existing vector store\n", "    vector_store.add_documents(chunks)\n", "    print(\"Documents added to vector store\")\n", "    return len(chunks)\n", "\n", "# Add the documents to the vector store\n", "num_chunks = add_documents_to_vectorstore(documents)\n", "print(f\"Added {num_chunks} chunks to vector store\")\n", "\n", "# Now run your evaluation with the agents\n", "# They will be able to retrieve information from the dataset"]}, {"cell_type": "code", "execution_count": 18, "id": "a7eef4ff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded story.txt: 3510 characters\n", "Split into 4 chunks\n", "Added 4 chunks to vector store at ./chroma_db_gemma\n", "\n", "Test Query: 'What is special about the Quantum Fern?'\n", "Retrieved Document Score: 0.4492\n", "Content: Within six months, the Quantum Garden had spread to cover a full hectare of Martian soil, creating a microclimate that was slowly but surely increasing oxygen levels in the surrounding atmosphere.\n", "\n", "El...\n"]}], "source": ["with open(\"story.txt\", \"r\") as file:\n", "    story_text = file.read()\n", "    print(f\"Loaded story.txt: {len(story_text)} characters\")\n", "    \n", "# Create document from story\n", "story_doc = Document(\n", "    page_content=story_text,\n", "    metadata={\"source\": \"story.txt\", \"type\": \"story\"}\n", ")\n", "\n", "# Split document into chunks\n", "text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)\n", "chunks = text_splitter.split_documents([story_doc])\n", "print(f\"Split into {len(chunks)} chunks\")\n", "\n", "# Add to vector store\n", "vector_store.add_documents(chunks)\n", "print(f\"Added {len(chunks)} chunks to vector store at ./chroma_db_gemma\")\n", "\n", "# Test retrieval\n", "test_query = \"What is special about the Quantum Fern?\"\n", "results = vector_store.similarity_search_with_score(test_query, k=1)\n", "doc, score = results[0]\n", "print(f\"\\nTest Query: '{test_query}'\")\n", "print(f\"Retrieved Document Score: {score:.4f}\")\n", "print(f\"Content: {doc.page_content[:200]}...\")\n"]}, {"cell_type": "code", "execution_count": 19, "id": "fa768d85", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== STEP 3: DEFINING AGENTS ===\n"]}], "source": ["print(\"\\n=== STEP 3: DEFINING AGENTS ===\")\n", "agents = {\n", "    #\"multi_step\": agentic_rag,\n", "    #\"decomposition\": lambda q: decomposition_agent(q)[\"final_answer\"],\n", "    #\"self_reflective\": lambda q: self_reflective_rag(q)[\"final_answer\"],\n", "    \"strategic\": strategic_multi_query_rag\n", "}"]}, {"cell_type": "code", "execution_count": 20, "id": "2da330b2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Evaluating: What is the Quantum Fern and what makes it so special? ---\n", "Response: Sure, here's a comprehensive answer that synthesizes information from all searches:\n", "\n", "The Quantum Fer...\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">✨ You're running DeepEval's latest <span style=\"color: #6a00ff; text-decoration-color: #6a00ff\">Answer Relevancy Metric</span>! <span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">(</span><span style=\"color: #374151; text-decoration-color: #374151\">using qwen2.</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">5</span><span style=\"color: #374151; text-decoration-color: #374151\">:latest </span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">(</span><span style=\"color: #374151; text-decoration-color: #374151\">Ollama</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">)</span><span style=\"color: #374151; text-decoration-color: #374151\">, </span><span style=\"color: #374151; text-decoration-color: #374151\">strict</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">False</span><span style=\"color: #374151; text-decoration-color: #374151\">, </span>\n", "<span style=\"color: #374151; text-decoration-color: #374151\">async_mode</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">True</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">)</span><span style=\"color: #374151; text-decoration-color: #374151\">...</span>\n", "</pre>\n"], "text/plain": ["✨ You're running DeepEval's latest \u001b[38;2;106;0;255mAnswer Relevancy Metric\u001b[0m! \u001b[1;38;2;55;65;81m(\u001b[0m\u001b[38;2;55;65;81musing qwen2.\u001b[0m\u001b[1;38;2;55;65;81m5\u001b[0m\u001b[38;2;55;65;81m:latest \u001b[0m\u001b[1;38;2;55;65;81m(\u001b[0m\u001b[38;2;55;65;81mOllama\u001b[0m\u001b[1;38;2;55;65;81m)\u001b[0m\u001b[38;2;55;65;81m, \u001b[0m\u001b[38;2;55;65;81mstrict\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mFalse\u001b[0m\u001b[38;2;55;65;81m, \u001b[0m\n", "\u001b[38;2;55;65;81masync_mode\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mTrue\u001b[0m\u001b[1;38;2;55;65;81m)\u001b[0m\u001b[38;2;55;65;81m...\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2b3da15fb66c423d97c0164aa7af308e", "version_major": 2, "version_minor": 0}, "text/plain": ["Output()"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======================================================================\n", "\n", "Metrics Summary\n", "\n", "  - ✅ Answer Relevancy (score: 1.0, threshold: 0.7, strict: False, evaluation model: qwen2.5:latest (Ollama), reason: The score is 1.00 because there are no irrelevant statements in the output that do not address the question about the Quantum Fern., error: None)\n", "\n", "For test case:\n", "\n", "  - input: What is the Quantum Fern and what makes it so special?\n", "  - actual output: Sure, here's a comprehensive answer that synthesizes information from all searches:\n", "\n", "The Quantum Fern is a remarkable fern discovered on Mars that exhibits quantum properties. The fern's discovery has revolutionized space colonization efforts and opened up new possibilities for life on other planets.\n", "\n", "**Key characteristics of the Quantum Fern:**\n", "\n", "* It is a quantum fern, meaning it exists in a superposition of quantum states.\n", "* It can share resources and create a collective root system across quantum states.\n", "* It produces a strange fruit with a shimmering blue skin and contains seeds that exist in quantum superposition.\n", "\n", "**Significance of the Quantum Fern:**\n", "\n", "* It provides a glimpse into the potential for life to exist in extreme environments.\n", "* It showcases the power of quantum mechanics and quantum field theory in understanding and manipulating biological systems.\n", "* It highlights the challenges and opportunities associated with creating sustainable life in space.\n", "\n", "**Relevance to Quantum Mechanics and Physics:**\n", "\n", "* The discovery sheds light on the intriguing properties of quantum systems and the possibility of life existing in non-traditional environments.\n", "* It contributes to our understanding of quantum field theory and its role in understanding complex biological systems.\n", "\n", "**Relevance to Space Exploration:**\n", "\n", "* The Quantum Fern's discovery has significant implications for space exploration.\n", "* It suggests that it may be possible to create self-sustaining ecosystems in space, which could provide a source of food and oxygen for future expeditions.\n", "* It also raises questions about the potential risks and challenges associated with bringing organisms from Earth to other planets.\n", "\n", "**Conclusion:**\n", "\n", "The Quantum Fern is a remarkable discovery that has profound implications for our understanding of life, space exploration, and the potential for civilization in the universe. It is a testament to the power of scientific inquiry and the limitless possibilities that await us in the exploration of our solar system and beyond.\n", "  - expected output: None\n", "  - context: None\n", "  - retrieval context: None\n", "\n", "======================================================================\n", "\n", "Overall Metric Pass Rates\n", "\n", "Answer Relevancy: 100.00% pass rate\n", "\n", "======================================================================\n", "\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "<span style=\"color: #05f58d; text-decoration-color: #05f58d\">✓</span> Tests finished 🎉! Run <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">'deepeval view'</span> to analyze, debug, and save evaluation results on <span style=\"color: #6a00ff; text-decoration-color: #6a00ff\">Confident AI</span>.\n", "\n", "</pre>\n"], "text/plain": ["\n", "\u001b[38;2;5;245;141m✓\u001b[0m Tests finished 🎉! Run \u001b[1;32m'deepeval view'\u001b[0m to analyze, debug, and save evaluation results on \u001b[38;2;106;0;255mConfident AI\u001b[0m.\n", "\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Evaluation complete: test_results=[TestResult(name='test_case_0', success=True, metrics_data=[MetricData(name='Answer Relevancy', threshold=0.7, success=True, score=1.0, reason='The score is 1.00 because there are no irrelevant statements in the output that do not address the question about the Quantum Fern.', strict_mode=False, evaluation_model='qwen2.5:latest (Ollama)', error=None, evaluation_cost=0.0, verbose_logs='Statements:\\n[\\n    \"The Quantum Fern is a remarkable fern discovered on Mars that exhibits quantum properties.\",\\n    \"Its discovery has revolutionized space colonization efforts and opened up new possibilities for life on other planets.\",\\n    \"It is a quantum fern, meaning it exists in a superposition of quantum states.\",\\n    \"It can share resources and create a collective root system across quantum states.\",\\n    \"It produces a strange fruit with a shimmering blue skin and contains seeds that exist in quantum superposition.\",\\n    \"It provides a glimpse into the potential for life to exist in extreme environments.\",\\n    \"It showcases the power of quantum mechanics and quantum field theory in understanding and manipulating biological systems.\",\\n    \"It highlights the challenges and opportunities associated with creating sustainable life in space.\",\\n    \"The discovery sheds light on the intriguing properties of quantum systems and the possibility of life existing in non-traditional environments.\",\\n    \"It contributes to our understanding of quantum field theory and its role in understanding complex biological systems.\",\\n    \"It suggests that it may be possible to create self-sustaining ecosystems in space, which could provide a source of food and oxygen for future expeditions.\",\\n    \"It also raises questions about the potential risks and challenges associated with bringing organisms from Earth to other planets.\",\\n    \"The Quantum Fern is a remarkable discovery that has profound implications for our understanding of life, space exploration, and the potential for civilization in the universe.\",\\n    \"It is a testament to the power of scientific inquiry and the limitless possibilities that await us in the exploration of our solar system and beyond.\"\\n] \\n \\nVerdicts:\\n[\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    }\\n]')], conversational=False, multimodal=False, input='What is the Quantum Fern and what makes it so special?', actual_output=\"Sure, here's a comprehensive answer that synthesizes information from all searches:\\n\\nThe Quantum Fern is a remarkable fern discovered on Mars that exhibits quantum properties. The fern's discovery has revolutionized space colonization efforts and opened up new possibilities for life on other planets.\\n\\n**Key characteristics of the Quantum Fern:**\\n\\n* It is a quantum fern, meaning it exists in a superposition of quantum states.\\n* It can share resources and create a collective root system across quantum states.\\n* It produces a strange fruit with a shimmering blue skin and contains seeds that exist in quantum superposition.\\n\\n**Significance of the Quantum Fern:**\\n\\n* It provides a glimpse into the potential for life to exist in extreme environments.\\n* It showcases the power of quantum mechanics and quantum field theory in understanding and manipulating biological systems.\\n* It highlights the challenges and opportunities associated with creating sustainable life in space.\\n\\n**Relevance to Quantum Mechanics and Physics:**\\n\\n* The discovery sheds light on the intriguing properties of quantum systems and the possibility of life existing in non-traditional environments.\\n* It contributes to our understanding of quantum field theory and its role in understanding complex biological systems.\\n\\n**Relevance to Space Exploration:**\\n\\n* The Quantum Fern's discovery has significant implications for space exploration.\\n* It suggests that it may be possible to create self-sustaining ecosystems in space, which could provide a source of food and oxygen for future expeditions.\\n* It also raises questions about the potential risks and challenges associated with bringing organisms from Earth to other planets.\\n\\n**Conclusion:**\\n\\nThe Quantum Fern is a remarkable discovery that has profound implications for our understanding of life, space exploration, and the potential for civilization in the universe. It is a testament to the power of scientific inquiry and the limitless possibilities that await us in the exploration of our solar system and beyond.\", expected_output=None, context=None, retrieval_context=None, additional_metadata=None)] confident_link=None\n", "\n", "--- Evaluating: Who is Dr. <PERSON>? ---\n", "Response: Dr. <PERSON> is a renowned quantum physicist and the lead researcher at the Institute for Quantum...\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">✨ You're running DeepEval's latest <span style=\"color: #6a00ff; text-decoration-color: #6a00ff\">Answer Relevancy Metric</span>! <span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">(</span><span style=\"color: #374151; text-decoration-color: #374151\">using qwen2.</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">5</span><span style=\"color: #374151; text-decoration-color: #374151\">:latest </span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">(</span><span style=\"color: #374151; text-decoration-color: #374151\">Ollama</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">)</span><span style=\"color: #374151; text-decoration-color: #374151\">, </span><span style=\"color: #374151; text-decoration-color: #374151\">strict</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">False</span><span style=\"color: #374151; text-decoration-color: #374151\">, </span>\n", "<span style=\"color: #374151; text-decoration-color: #374151\">async_mode</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">True</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">)</span><span style=\"color: #374151; text-decoration-color: #374151\">...</span>\n", "</pre>\n"], "text/plain": ["✨ You're running DeepEval's latest \u001b[38;2;106;0;255mAnswer Relevancy Metric\u001b[0m! \u001b[1;38;2;55;65;81m(\u001b[0m\u001b[38;2;55;65;81musing qwen2.\u001b[0m\u001b[1;38;2;55;65;81m5\u001b[0m\u001b[38;2;55;65;81m:latest \u001b[0m\u001b[1;38;2;55;65;81m(\u001b[0m\u001b[38;2;55;65;81mOllama\u001b[0m\u001b[1;38;2;55;65;81m)\u001b[0m\u001b[38;2;55;65;81m, \u001b[0m\u001b[38;2;55;65;81mstrict\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mFalse\u001b[0m\u001b[38;2;55;65;81m, \u001b[0m\n", "\u001b[38;2;55;65;81masync_mode\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mTrue\u001b[0m\u001b[1;38;2;55;65;81m)\u001b[0m\u001b[38;2;55;65;81m...\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4ebeab6082fa4e88a2a6ec0b3ebade32", "version_major": 2, "version_minor": 0}, "text/plain": ["Output()"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======================================================================\n", "\n", "Metrics Summary\n", "\n", "  - ✅ Answer Relevancy (score: 1.0, threshold: 0.7, strict: False, evaluation model: qwen2.5:latest (Ollama), reason: The score is 1.00 because there are no irrelevant statements in the output, making it a perfect score., error: None)\n", "\n", "For test case:\n", "\n", "  - input: Who is Dr. <PERSON>?\n", "  - actual output: Dr. <PERSON> is a renowned quantum physicist and the lead researcher at the Institute for Quantum Information and Matter in Singapore. Her research focuses on understanding and harnessing quantum phenomena to advance scientific research and technological innovation.\n", "\n", "Her contributions to the field of quantum machine translation have significantly advanced our understanding of how quantum states can be represented and manipulated. She has also made groundbreaking discoveries in the field of artificial intelligence, particularly in the area of deep learning.\n", "\n", "Dr<PERSON>'s groundbreaking achievements have earned her numerous awards and recognitions, including the Global Science Prize and numerous other honors. She is a true pioneer in the field of quantum science and technology.\n", "  - expected output: None\n", "  - context: None\n", "  - retrieval context: None\n", "\n", "======================================================================\n", "\n", "Overall Metric Pass Rates\n", "\n", "Answer Relevancy: 100.00% pass rate\n", "\n", "======================================================================\n", "\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "<span style=\"color: #05f58d; text-decoration-color: #05f58d\">✓</span> Tests finished 🎉! Run <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">'deepeval view'</span> to analyze, debug, and save evaluation results on <span style=\"color: #6a00ff; text-decoration-color: #6a00ff\">Confident AI</span>.\n", "\n", "</pre>\n"], "text/plain": ["\n", "\u001b[38;2;5;245;141m✓\u001b[0m Tests finished 🎉! Run \u001b[1;32m'deepeval view'\u001b[0m to analyze, debug, and save evaluation results on \u001b[38;2;106;0;255mConfident AI\u001b[0m.\n", "\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Evaluation complete: test_results=[TestResult(name='test_case_0', success=True, metrics_data=[MetricData(name='Answer Relevancy', threshold=0.7, success=True, score=1.0, reason='The score is 1.00 because there are no irrelevant statements in the output, making it a perfect score.', strict_mode=False, evaluation_model='qwen2.5:latest (Ollama)', error=None, evaluation_cost=0.0, verbose_logs='Statements:\\n[\\n    \"Dr. <PERSON> is a renowned quantum physicist.\",\\n    \"She is the lead researcher at the Institute for Quantum Information and Matter in Singapore.\",\\n    \"Her research focuses on understanding and harnessing quantum phenomena to advance scientific research and technological innovation.\",\\n    \"Her contributions to the field of quantum machine translation have significantly advanced our understanding of how quantum states can be represented and manipulated.\",\\n    \"She has also made groundbreaking discoveries in the field of artificial intelligence, particularly in the area of deep learning.\",\\n    \"Dr. <PERSON>\\'s groundbreaking achievements have earned her numerous awards and recognitions, including the Global Science Prize.\",\\n    \"She is a true pioneer in the field of quantum science and technology.\"\\n] \\n \\nVerdicts:\\n[\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    }\\n]')], conversational=False, multimodal=False, input='Who is Dr. Elena Reyes?', actual_output=\"Dr. Elena Reyes is a renowned quantum physicist and the lead researcher at the Institute for Quantum Information and Matter in Singapore. Her research focuses on understanding and harnessing quantum phenomena to advance scientific research and technological innovation.\\n\\nHer contributions to the field of quantum machine translation have significantly advanced our understanding of how quantum states can be represented and manipulated. She has also made groundbreaking discoveries in the field of artificial intelligence, particularly in the area of deep learning.\\n\\nDr. Reyes's groundbreaking achievements have earned her numerous awards and recognitions, including the Global Science Prize and numerous other honors. She is a true pioneer in the field of quantum science and technology.\", expected_output=None, context=None, retrieval_context=None, additional_metadata=None)] confident_link=None\n", "\n", "--- Evaluating: What was Dr. <PERSON><PERSON><PERSON> concerned about? ---\n", "Response: Dr. <PERSON><PERSON><PERSON> was concerned about potential risks associated with the overall design and functi...\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">✨ You're running DeepEval's latest <span style=\"color: #6a00ff; text-decoration-color: #6a00ff\">Answer Relevancy Metric</span>! <span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">(</span><span style=\"color: #374151; text-decoration-color: #374151\">using qwen2.</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">5</span><span style=\"color: #374151; text-decoration-color: #374151\">:latest </span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">(</span><span style=\"color: #374151; text-decoration-color: #374151\">Ollama</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">)</span><span style=\"color: #374151; text-decoration-color: #374151\">, </span><span style=\"color: #374151; text-decoration-color: #374151\">strict</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">False</span><span style=\"color: #374151; text-decoration-color: #374151\">, </span>\n", "<span style=\"color: #374151; text-decoration-color: #374151\">async_mode</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">True</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">)</span><span style=\"color: #374151; text-decoration-color: #374151\">...</span>\n", "</pre>\n"], "text/plain": ["✨ You're running DeepEval's latest \u001b[38;2;106;0;255mAnswer Relevancy Metric\u001b[0m! \u001b[1;38;2;55;65;81m(\u001b[0m\u001b[38;2;55;65;81musing qwen2.\u001b[0m\u001b[1;38;2;55;65;81m5\u001b[0m\u001b[38;2;55;65;81m:latest \u001b[0m\u001b[1;38;2;55;65;81m(\u001b[0m\u001b[38;2;55;65;81mOllama\u001b[0m\u001b[1;38;2;55;65;81m)\u001b[0m\u001b[38;2;55;65;81m, \u001b[0m\u001b[38;2;55;65;81mstrict\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mFalse\u001b[0m\u001b[38;2;55;65;81m, \u001b[0m\n", "\u001b[38;2;55;65;81masync_mode\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mTrue\u001b[0m\u001b[1;38;2;55;65;81m)\u001b[0m\u001b[38;2;55;65;81m...\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a73df0cf62804195a3f8b3e2058584eb", "version_major": 2, "version_minor": 0}, "text/plain": ["Output()"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======================================================================\n", "\n", "Metrics Summary\n", "\n", "  - ❌ Answer Relevancy (score: 0.6666666666666666, threshold: 0.7, strict: False, evaluation model: qwen2.5:latest (Ollama), reason: The score is 0.67 because the output contained statements unrelated to Dr<PERSON><PERSON>'s concerns, specifically about potential risks of plant states and invasive species, which are not relevant to her AI system worries., error: None)\n", "\n", "For test case:\n", "\n", "  - input: What was Dr. <PERSON><PERSON><PERSON> concerned about?\n", "  - actual output: Dr. <PERSON><PERSON><PERSON> was concerned about potential risks associated with the overall design and functionality of the AI system she was developing. If these plants can share resources across quantum states, it could lead to unintended consequences, such as the spread of invasive species or the creation of new forms of life that could pose a threat to humanity. Additionally, there was a risk that the plants could escape containment and become a danger to the environment or to other settlements.\n", "  - expected output: None\n", "  - context: None\n", "  - retrieval context: None\n", "\n", "======================================================================\n", "\n", "Overall Metric Pass Rates\n", "\n", "Answer Relevancy: 0.00% pass rate\n", "\n", "======================================================================\n", "\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "<span style=\"color: #05f58d; text-decoration-color: #05f58d\">✓</span> Tests finished 🎉! Run <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">'deepeval view'</span> to analyze, debug, and save evaluation results on <span style=\"color: #6a00ff; text-decoration-color: #6a00ff\">Confident AI</span>.\n", "\n", "</pre>\n"], "text/plain": ["\n", "\u001b[38;2;5;245;141m✓\u001b[0m Tests finished 🎉! Run \u001b[1;32m'deepeval view'\u001b[0m to analyze, debug, and save evaluation results on \u001b[38;2;106;0;255mConfident AI\u001b[0m.\n", "\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Evaluation complete: test_results=[TestResult(name='test_case_0', success=False, metrics_data=[MetricData(name='Answer Relevancy', threshold=0.7, success=False, score=0.6666666666666666, reason=\"The score is 0.67 because the output contained statements unrelated to Dr<PERSON><PERSON><PERSON>'s concerns, specifically about potential risks of plant states and invasive species, which are not relevant to her AI system worries.\", strict_mode=False, evaluation_model='qwen2.5:latest (Ollama)', error=None, evaluation_cost=0.0, verbose_logs='Statements:\\n[\\n    \"Dr. <PERSON><PERSON><PERSON> was concerned about potential risks associated with the overall design and functionality of the AI system she was developing.\",\\n    \"If these plants can share resources across quantum states, it could lead to unintended consequences.\",\\n    \"Such consequences might include the spread of invasive species.\",\\n    \"There is a risk that the plants could escape containment.\",\\n    \"The escaped plants could become a danger to the environment.\",\\n    \"The escaped plants could also pose a threat to other settlements.\"\\n] \\n \\nVerdicts:\\n[\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"no\",\\n        \"reason\": \"The statement is about potential risks associated with plant states, which are unrelated to <PERSON><PERSON><PERSON><PERSON>'s concerns about the AI system.\"\\n    },\\n    {\\n        \"verdict\": \"no\",\\n        \"reason\": \"This statement discusses the spread of invasive species, which is not related to Dr. Yoshiko <PERSON>\\'s concerns about the AI system.\"\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    }\\n]')], conversational=False, multimodal=False, input='What was Dr. Yoshiko Tanaka concerned about?', actual_output='Dr. Yoshiko Tanaka was concerned about potential risks associated with the overall design and functionality of the AI system she was developing. If these plants can share resources across quantum states, it could lead to unintended consequences, such as the spread of invasive species or the creation of new forms of life that could pose a threat to humanity. Additionally, there was a risk that the plants could escape containment and become a danger to the environment or to other settlements.', expected_output=None, context=None, retrieval_context=None, additional_metadata=None)] confident_link=None\n", "\n", "--- Evaluating: How did the Quantum Garden impact Mars colonization? ---\n", "Response: The Quantum Garden played a significant role in the advancement of space colonization efforts on Mar...\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">✨ You're running DeepEval's latest <span style=\"color: #6a00ff; text-decoration-color: #6a00ff\">Answer Relevancy Metric</span>! <span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">(</span><span style=\"color: #374151; text-decoration-color: #374151\">using qwen2.</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">5</span><span style=\"color: #374151; text-decoration-color: #374151\">:latest </span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">(</span><span style=\"color: #374151; text-decoration-color: #374151\">Ollama</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">)</span><span style=\"color: #374151; text-decoration-color: #374151\">, </span><span style=\"color: #374151; text-decoration-color: #374151\">strict</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">False</span><span style=\"color: #374151; text-decoration-color: #374151\">, </span>\n", "<span style=\"color: #374151; text-decoration-color: #374151\">async_mode</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">True</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">)</span><span style=\"color: #374151; text-decoration-color: #374151\">...</span>\n", "</pre>\n"], "text/plain": ["✨ You're running DeepEval's latest \u001b[38;2;106;0;255mAnswer Relevancy Metric\u001b[0m! \u001b[1;38;2;55;65;81m(\u001b[0m\u001b[38;2;55;65;81musing qwen2.\u001b[0m\u001b[1;38;2;55;65;81m5\u001b[0m\u001b[38;2;55;65;81m:latest \u001b[0m\u001b[1;38;2;55;65;81m(\u001b[0m\u001b[38;2;55;65;81mOllama\u001b[0m\u001b[1;38;2;55;65;81m)\u001b[0m\u001b[38;2;55;65;81m, \u001b[0m\u001b[38;2;55;65;81mstrict\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mFalse\u001b[0m\u001b[38;2;55;65;81m, \u001b[0m\n", "\u001b[38;2;55;65;81masync_mode\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mTrue\u001b[0m\u001b[1;38;2;55;65;81m)\u001b[0m\u001b[38;2;55;65;81m...\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d9d195c82736479abe698bf488cfc572", "version_major": 2, "version_minor": 0}, "text/plain": ["Output()"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======================================================================\n", "\n", "Metrics Summary\n", "\n", "  - ✅ Answer Relevancy (score: 1.0, threshold: 0.7, strict: False, evaluation model: qwen2.5:latest (Ollama), reason: The score is 1.00 because there are no irrelevant statements in the output to affect the score negatively., error: None)\n", "\n", "For test case:\n", "\n", "  - input: How did the Quantum Garden impact Mars colonization?\n", "  - actual output: The Quantum Garden played a significant role in the advancement of space colonization efforts on Mars. <PERSON>'s discovery revolutionized the field by creating a microclimate that gradually increased oxygen levels in the atmosphere. This breakthrough paved the way for the establishment of the first human settlement not requiring artificial life support, setting a precedent for future space endeavors.\n", "\n", "The Quantum Garden's impact extended beyond the initial microcosm. <PERSON>'s team discovered that the plants could adapt to any environment instantly by collapsing into the most favorable quantum state. This discovery opened up new possibilities for terraforming Mars and establishing sustainable settlements on other planets.\n", "\n", "The Quantum Garden's discovery also highlighted the potential of using quantum technology to manipulate physical constants and create desired environments. This technology could have significant implications for various fields, including space exploration, medicine, and environmental science.\n", "  - expected output: None\n", "  - context: None\n", "  - retrieval context: None\n", "\n", "======================================================================\n", "\n", "Overall Metric Pass Rates\n", "\n", "Answer Relevancy: 100.00% pass rate\n", "\n", "======================================================================\n", "\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "<span style=\"color: #05f58d; text-decoration-color: #05f58d\">✓</span> Tests finished 🎉! Run <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">'deepeval view'</span> to analyze, debug, and save evaluation results on <span style=\"color: #6a00ff; text-decoration-color: #6a00ff\">Confident AI</span>.\n", "\n", "</pre>\n"], "text/plain": ["\n", "\u001b[38;2;5;245;141m✓\u001b[0m Tests finished 🎉! Run \u001b[1;32m'deepeval view'\u001b[0m to analyze, debug, and save evaluation results on \u001b[38;2;106;0;255mConfident AI\u001b[0m.\n", "\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Evaluation complete: test_results=[TestResult(name='test_case_0', success=True, metrics_data=[MetricData(name='Answer Relevancy', threshold=0.7, success=True, score=1.0, reason='The score is 1.00 because there are no irrelevant statements in the output to affect the score negatively.', strict_mode=False, evaluation_model='qwen2.5:latest (Ollama)', error=None, evaluation_cost=0.0, verbose_logs='Statements:\\n[\\n    \"The Quantum Garden played a significant role in the advancement of space colonization efforts on Mars.\",\\n    \"Elena\\'s discovery revolutionized the field by creating a microclimate that gradually increased oxygen levels in the atmosphere.\",\\n    \"This breakthrough paved the way for the establishment of the first human settlement not requiring artificial life support, setting a precedent for future space endeavors.\",\\n    \"The Quantum Garden\\'s impact extended beyond the initial microcosm.\",\\n    \"<PERSON>'s team discovered that the plants could adapt to any environment instantly by collapsing into the most favorable quantum state.\",\\n    \"This discovery opened up new possibilities for terraforming Mars and establishing sustainable settlements on other planets.\",\\n    \"The Quantum Garden\\'s discovery also highlighted the potential of using quantum technology to manipulate physical constants and create desired environments.\",\\n    \"This technology could have significant implications for various fields, including space exploration, medicine, and environmental science.\"\\n] \\n \\nVerdicts:\\n[\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": \"The statement directly addresses the impact of the Quantum Garden on Mars colonization.\"\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": \"This statement explains how the discovery led to the establishment of a human settlement, which is relevant to the impact on Mars colonization.\"\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": \"The statement describes an aspect of the Quantum Garden\\'s broader impact that could be related to Mars colonization efforts.\"\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": \"This statement further elaborates on how the discovery could enable sustainable settlements, which is directly relevant to the topic.\"\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": \"The statement discusses potential applications of quantum technology that are pertinent to Mars colonization and space exploration in general.\"\\n    }\\n]')], conversational=False, multimodal=False, input='How did the Quantum Garden impact Mars colonization?', actual_output=\"The Quantum Garden played a significant role in the advancement of space colonization efforts on Mars. Elena's discovery revolutionized the field by creating a microclimate that gradually increased oxygen levels in the atmosphere. This breakthrough paved the way for the establishment of the first human settlement not requiring artificial life support, setting a precedent for future space endeavors.\\n\\nThe Quantum Garden's impact extended beyond the initial microcosm. Elena's team discovered that the plants could adapt to any environment instantly by collapsing into the most favorable quantum state. This discovery opened up new possibilities for terraforming Mars and establishing sustainable settlements on other planets.\\n\\nThe Quantum Garden's discovery also highlighted the potential of using quantum technology to manipulate physical constants and create desired environments. This technology could have significant implications for various fields, including space exploration, medicine, and environmental science.\", expected_output=None, context=None, retrieval_context=None, additional_metadata=None)] confident_link=None\n", "\n", "--- Evaluating: What did <PERSON> suggest to enhance the communication between Quantum Ferns? ---\n", "Response: Sure, here's a comprehensive answer to the question:\n", "\n", "The passage provides information about <PERSON> ...\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">✨ You're running DeepEval's latest <span style=\"color: #6a00ff; text-decoration-color: #6a00ff\">Answer Relevancy Metric</span>! <span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">(</span><span style=\"color: #374151; text-decoration-color: #374151\">using qwen2.</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">5</span><span style=\"color: #374151; text-decoration-color: #374151\">:latest </span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">(</span><span style=\"color: #374151; text-decoration-color: #374151\">Ollama</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">)</span><span style=\"color: #374151; text-decoration-color: #374151\">, </span><span style=\"color: #374151; text-decoration-color: #374151\">strict</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">False</span><span style=\"color: #374151; text-decoration-color: #374151\">, </span>\n", "<span style=\"color: #374151; text-decoration-color: #374151\">async_mode</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">True</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">)</span><span style=\"color: #374151; text-decoration-color: #374151\">...</span>\n", "</pre>\n"], "text/plain": ["✨ You're running DeepEval's latest \u001b[38;2;106;0;255mAnswer Relevancy Metric\u001b[0m! \u001b[1;38;2;55;65;81m(\u001b[0m\u001b[38;2;55;65;81musing qwen2.\u001b[0m\u001b[1;38;2;55;65;81m5\u001b[0m\u001b[38;2;55;65;81m:latest \u001b[0m\u001b[1;38;2;55;65;81m(\u001b[0m\u001b[38;2;55;65;81mOllama\u001b[0m\u001b[1;38;2;55;65;81m)\u001b[0m\u001b[38;2;55;65;81m, \u001b[0m\u001b[38;2;55;65;81mstrict\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mFalse\u001b[0m\u001b[38;2;55;65;81m, \u001b[0m\n", "\u001b[38;2;55;65;81masync_mode\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mTrue\u001b[0m\u001b[1;38;2;55;65;81m)\u001b[0m\u001b[38;2;55;65;81m...\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "54ae255e4cd74343a9bcab8d923f9490", "version_major": 2, "version_minor": 0}, "text/plain": ["Output()"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======================================================================\n", "\n", "Metrics Summary\n", "\n", "  - ❌ Answer Relevancy (score: 0.16666666666666666, threshold: 0.7, strict: False, evaluation model: qwen2.5:latest (Ollama), reason: The score is 0.17 because most of the irrelevant statements focus on implementation details, outcomes, and risks rather than directly addressing what <PERSON> suggested about enhancing communication between Quantum Ferns., error: None)\n", "\n", "For test case:\n", "\n", "  - input: What did <PERSON> suggest to enhance the communication between Quantum Ferns?\n", "  - actual output: Sure, here's a comprehensive answer to the question:\n", "\n", "The passage provides information about <PERSON>'s suggestion to enhance the communication between Quantum Ferns.\n", "\n", "**<PERSON> suggested increasing the quantum field strength to enhance the communication between Quantum Ferns.**\n", "\n", "<PERSON>'s suggestion is mentioned in the passage as a means to share resources across quantum states and improve the overall efficiency of the communication process. However, the passage does not provide specific details about how this suggestion would be implemented or what the expected outcomes would be.\n", "\n", "The passage also mentions the potential risks associated with this suggestion, such as the need to ensure that the plants can be contained and the risk of spreading in ways that can be unpredictable.\n", "  - expected output: None\n", "  - context: None\n", "  - retrieval context: None\n", "\n", "======================================================================\n", "\n", "Overall Metric Pass Rates\n", "\n", "Answer Relevancy: 0.00% pass rate\n", "\n", "======================================================================\n", "\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "<span style=\"color: #05f58d; text-decoration-color: #05f58d\">✓</span> Tests finished 🎉! Run <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">'deepeval view'</span> to analyze, debug, and save evaluation results on <span style=\"color: #6a00ff; text-decoration-color: #6a00ff\">Confident AI</span>.\n", "\n", "</pre>\n"], "text/plain": ["\n", "\u001b[38;2;5;245;141m✓\u001b[0m Tests finished 🎉! Run \u001b[1;32m'deepeval view'\u001b[0m to analyze, debug, and save evaluation results on \u001b[38;2;106;0;255mConfident AI\u001b[0m.\n", "\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Evaluation complete: test_results=[TestResult(name='test_case_0', success=False, metrics_data=[MetricData(name='Answer Relevancy', threshold=0.7, success=False, score=0.16666666666666666, reason='The score is 0.17 because most of the irrelevant statements focus on implementation details, outcomes, and risks rather than directly addressing what <PERSON> suggested about enhancing communication between Quantum Ferns.', strict_mode=False, evaluation_model='qwen2.5:latest (Ollama)', error=None, evaluation_cost=0.0, verbose_logs='Statements:\\n[\\n    \"<PERSON> suggested increasing the quantum field strength to enhance the communication between Quantum Ferns.\",\\n    \"The passage does not provide specific details about how this suggestion would be implemented.\",\\n    \"The expected outcomes of the suggestion are not provided in the passage.\",\\n    \"There is a mention of potential risks associated with the suggestion.\",\\n    \"These risks include ensuring that the plants can be contained.\",\\n    \"Another risk mentioned is the possibility of spreading in unpredictable ways.\"\\n] \\n \\nVerdicts:\\n[\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"no\",\\n        \"reason\": \"This statement does not directly address what <PERSON> suggested, but rather how the suggestion would be implemented.\"\\n    },\\n    {\\n        \"verdict\": \"no\",\\n        \"reason\": \"This statement does not directly address what <PERSON> suggested, but rather the outcomes of the suggestion.\"\\n    },\\n    {\\n        \"verdict\": \"no\",\\n        \"reason\": \"This statement provides information about risks associated with the suggestion, but does not state the suggestion itself.\"\\n    },\\n    {\\n        \"verdict\": \"no\",\\n        \"reason\": \"This statement discusses a risk related to containment, which is not directly addressing the suggestion made by Marcus Chen.\"\\n    },\\n    {\\n        \"verdict\": \"no\",\\n        \"reason\": \"This statement talks about an additional risk of spreading unpredictably, which is not directly addressing the suggestion made by Marcus Chen.\"\\n    }\\n]')], conversational=False, multimodal=False, input='What did Marcus Chen suggest to enhance the communication between Quantum Ferns?', actual_output=\"Sure, here's a comprehensive answer to the question:\\n\\nThe passage provides information about Marcus Chen's suggestion to enhance the communication between Quantum Ferns.\\n\\n**Marcus Chen suggested increasing the quantum field strength to enhance the communication between Quantum Ferns.**\\n\\nChen's suggestion is mentioned in the passage as a means to share resources across quantum states and improve the overall efficiency of the communication process. However, the passage does not provide specific details about how this suggestion would be implemented or what the expected outcomes would be.\\n\\nThe passage also mentions the potential risks associated with this suggestion, such as the need to ensure that the plants can be contained and the risk of spreading in ways that can be unpredictable.\", expected_output=None, context=None, retrieval_context=None, additional_metadata=None)] confident_link=None\n", "\n", "--- Evaluating: What unusual behavior did <PERSON> notice in greenhouse chamber 7? ---\n", "Response: The information provided does not mention anything about an unusual behavior in greenhouse chamber 7...\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">✨ You're running DeepEval's latest <span style=\"color: #6a00ff; text-decoration-color: #6a00ff\">Answer Relevancy Metric</span>! <span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">(</span><span style=\"color: #374151; text-decoration-color: #374151\">using qwen2.</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">5</span><span style=\"color: #374151; text-decoration-color: #374151\">:latest </span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">(</span><span style=\"color: #374151; text-decoration-color: #374151\">Ollama</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">)</span><span style=\"color: #374151; text-decoration-color: #374151\">, </span><span style=\"color: #374151; text-decoration-color: #374151\">strict</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">False</span><span style=\"color: #374151; text-decoration-color: #374151\">, </span>\n", "<span style=\"color: #374151; text-decoration-color: #374151\">async_mode</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">True</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">)</span><span style=\"color: #374151; text-decoration-color: #374151\">...</span>\n", "</pre>\n"], "text/plain": ["✨ You're running DeepEval's latest \u001b[38;2;106;0;255mAnswer Relevancy Metric\u001b[0m! \u001b[1;38;2;55;65;81m(\u001b[0m\u001b[38;2;55;65;81musing qwen2.\u001b[0m\u001b[1;38;2;55;65;81m5\u001b[0m\u001b[38;2;55;65;81m:latest \u001b[0m\u001b[1;38;2;55;65;81m(\u001b[0m\u001b[38;2;55;65;81mOllama\u001b[0m\u001b[1;38;2;55;65;81m)\u001b[0m\u001b[38;2;55;65;81m, \u001b[0m\u001b[38;2;55;65;81mstrict\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mFalse\u001b[0m\u001b[38;2;55;65;81m, \u001b[0m\n", "\u001b[38;2;55;65;81masync_mode\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mTrue\u001b[0m\u001b[1;38;2;55;65;81m)\u001b[0m\u001b[38;2;55;65;81m...\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8873a909f5144ecfb5acd272f9da7500", "version_major": 2, "version_minor": 0}, "text/plain": ["Output()"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======================================================================\n", "\n", "Metrics Summary\n", "\n", "  - ❌ Answer Relevancy (score: 0.0, threshold: 0.7, strict: False, evaluation model: qwen2.5:latest (Ollama), reason: The score is 0.00 because the response directly addresses the question by not including any irrelevant statements, but it still lacks relevant information about the unusual behavior <PERSON> noticed in greenhouse chamber 7., error: None)\n", "\n", "For test case:\n", "\n", "  - input: What unusual behavior did <PERSON> notice in greenhouse chamber 7?\n", "  - actual output: The information provided does not mention anything about an unusual behavior in greenhouse chamber 7, so I cannot answer this question from the provided context.\n", "  - expected output: None\n", "  - context: None\n", "  - retrieval context: None\n", "\n", "======================================================================\n", "\n", "Overall Metric Pass Rates\n", "\n", "Answer Relevancy: 0.00% pass rate\n", "\n", "======================================================================\n", "\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "<span style=\"color: #05f58d; text-decoration-color: #05f58d\">✓</span> Tests finished 🎉! Run <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">'deepeval view'</span> to analyze, debug, and save evaluation results on <span style=\"color: #6a00ff; text-decoration-color: #6a00ff\">Confident AI</span>.\n", "\n", "</pre>\n"], "text/plain": ["\n", "\u001b[38;2;5;245;141m✓\u001b[0m Tests finished 🎉! Run \u001b[1;32m'deepeval view'\u001b[0m to analyze, debug, and save evaluation results on \u001b[38;2;106;0;255mConfident AI\u001b[0m.\n", "\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Evaluation complete: test_results=[TestResult(name='test_case_0', success=False, metrics_data=[MetricData(name='Answer Relevancy', threshold=0.7, success=False, score=0.0, reason='The score is 0.00 because the response directly addresses the question by not including any irrelevant statements, but it still lacks relevant information about the unusual behavior <PERSON> noticed in greenhouse chamber 7.', strict_mode=False, evaluation_model='qwen2.5:latest (Ollama)', error=None, evaluation_cost=0.0, verbose_logs='Statements:\\n[\\n    \"The information provided does not mention anything about an unusual behavior in greenhouse chamber 7.\"\\n] \\n \\nVerdicts:\\n[\\n    {\\n        \"verdict\": \"no\",\\n        \"reason\": \"The statement does not provide any information related to the unusual behavior <PERSON> noticed in greenhouse chamber 7.\"\\n    }\\n]')], conversational=False, multimodal=False, input='What unusual behavior did <PERSON> notice in greenhouse chamber 7?', actual_output='The information provided does not mention anything about an unusual behavior in greenhouse chamber 7, so I cannot answer this question from the provided context.', expected_output=None, context=None, retrieval_context=None, additional_metadata=None)] confident_link=None\n", "\n", "--- Evaluating: What was the purpose of the 'Schrödinger's Garden' project? ---\n", "Response: The Schr<PERSON>dinger's Garden project aimed to explore the potential of quantum field theory to create a ...\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">✨ You're running DeepEval's latest <span style=\"color: #6a00ff; text-decoration-color: #6a00ff\">Answer Relevancy Metric</span>! <span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">(</span><span style=\"color: #374151; text-decoration-color: #374151\">using qwen2.</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">5</span><span style=\"color: #374151; text-decoration-color: #374151\">:latest </span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">(</span><span style=\"color: #374151; text-decoration-color: #374151\">Ollama</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">)</span><span style=\"color: #374151; text-decoration-color: #374151\">, </span><span style=\"color: #374151; text-decoration-color: #374151\">strict</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">False</span><span style=\"color: #374151; text-decoration-color: #374151\">, </span>\n", "<span style=\"color: #374151; text-decoration-color: #374151\">async_mode</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">True</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">)</span><span style=\"color: #374151; text-decoration-color: #374151\">...</span>\n", "</pre>\n"], "text/plain": ["✨ You're running DeepEval's latest \u001b[38;2;106;0;255mAnswer Relevancy Metric\u001b[0m! \u001b[1;38;2;55;65;81m(\u001b[0m\u001b[38;2;55;65;81musing qwen2.\u001b[0m\u001b[1;38;2;55;65;81m5\u001b[0m\u001b[38;2;55;65;81m:latest \u001b[0m\u001b[1;38;2;55;65;81m(\u001b[0m\u001b[38;2;55;65;81mOllama\u001b[0m\u001b[1;38;2;55;65;81m)\u001b[0m\u001b[38;2;55;65;81m, \u001b[0m\u001b[38;2;55;65;81mstrict\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mFalse\u001b[0m\u001b[38;2;55;65;81m, \u001b[0m\n", "\u001b[38;2;55;65;81masync_mode\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mTrue\u001b[0m\u001b[1;38;2;55;65;81m)\u001b[0m\u001b[38;2;55;65;81m...\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9278875dda134c4c8cdd876aacab313d", "version_major": 2, "version_minor": 0}, "text/plain": ["Output()"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======================================================================\n", "\n", "Metrics Summary\n", "\n", "  - ✅ Answer Relevancy (score: 0.9090909090909091, threshold: 0.7, strict: False, evaluation model: qwen2.5:latest (Ollama), reason: The score is 0.91 because the output mentions the project's impact and future influence, which are not directly relevant to the asked question about the purpose., error: None)\n", "\n", "For test case:\n", "\n", "  - input: What was the purpose of the 'Schrödinger's Garden' project?\n", "  - actual output: The Schrödinger's Garden project aimed to explore the potential of quantum field theory to create a stable, self-sustaining microclimate on Mars. The project involved several key innovations, including:\n", "\n", "**1. Quantum field enhancement:** Researchers proposed increasing the quantum field strength in the quantum garden to enhance the microclimate's effect.\n", "\n", "**2. Collective root system:** By manipulating the quantum field strength, they created a collective root system that spanned across quantum states.\n", "\n", "**3. Fruit production:** One of the plants produced a strange fruit that contained seeds in a state that defied biological laws.\n", "\n", "**4. Risks and limitations:** The project was concerned about potential risks and limitations, such as the plants escaping containment and spreading in unpredictable ways.\n", "\n", "**5. Machine learning:** Researchers used machine learning techniques to represent and analyze the complex interactions between the plants and the quantum field.\n", "\n", "**6. Impact and legacy:** The <PERSON>hr<PERSON><PERSON><PERSON>'s Garden project paved the way for future research on quantum field theory and its applications to space exploration.\n", "\n", "**Key takeaways:**\n", "\n", "* The project explored the potential of quantum field theory to create a stable and self-sustaining microclimate in space.\n", "* Researchers achieved significant advancements in understanding the interactions between quantum fields and matter.\n", "* The project raised important questions about the risks and limitations associated with using quantum field theory in space.\n", "* The project had a significant impact on the field of quantum field theory and inspired future research in the area.\n", "  - expected output: None\n", "  - context: None\n", "  - retrieval context: None\n", "\n", "======================================================================\n", "\n", "Overall Metric Pass Rates\n", "\n", "Answer Relevancy: 100.00% pass rate\n", "\n", "======================================================================\n", "\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "<span style=\"color: #05f58d; text-decoration-color: #05f58d\">✓</span> Tests finished 🎉! Run <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">'deepeval view'</span> to analyze, debug, and save evaluation results on <span style=\"color: #6a00ff; text-decoration-color: #6a00ff\">Confident AI</span>.\n", "\n", "</pre>\n"], "text/plain": ["\n", "\u001b[38;2;5;245;141m✓\u001b[0m Tests finished 🎉! Run \u001b[1;32m'deepeval view'\u001b[0m to analyze, debug, and save evaluation results on \u001b[38;2;106;0;255mConfident AI\u001b[0m.\n", "\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Evaluation complete: test_results=[TestResult(name='test_case_0', success=True, metrics_data=[MetricData(name='Answer Relevancy', threshold=0.7, success=True, score=0.9090909090909091, reason=\"The score is 0.91 because the output mentions the project's impact and future influence, which are not directly relevant to the asked question about the purpose.\", strict_mode=False, evaluation_model='qwen2.5:latest (Ollama)', error=None, evaluation_cost=0.0, verbose_logs='Statements:\\n[\\n    \"The Schrödinger\\'s Garden project aimed to explore the potential of quantum field theory to create a stable, self-sustaining microclimate on Mars.\",\\n    \"Researchers proposed increasing the quantum field strength in the quantum garden to enhance the microclimate\\'s effect.\",\\n    \"They created a collective root system that spanned across quantum states by manipulating the quantum field strength.\",\\n    \"One of the plants produced a strange fruit that contained seeds in a state that defied biological laws.\",\\n    \"The project was concerned about potential risks and limitations, such as the plants escaping containment and spreading in unpredictable ways.\",\\n    \"Researchers used machine learning techniques to represent and analyze the complex interactions between the plants and the quantum field.\",\\n    \"The Schrödinger\\'s Garden project paved the way for future research on quantum field theory and its applications to space exploration.\",\\n    \"The project explored the potential of quantum field theory to create a stable and self-sustaining microclimate in space.\",\\n    \"Researchers achieved significant advancements in understanding the interactions between quantum fields and matter.\",\\n    \"The project raised important questions about the risks and limitations associated with using quantum field theory in space.\",\\n    \"The project had a significant impact on the field of quantum field theory and inspired future research in the area.\"\\n] \\n \\nVerdicts:\\n[\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"no\",\\n        \"reason\": \"This statement does not directly address the purpose of the project but rather its impact and future influence.\"\\n    }\\n]')], conversational=False, multimodal=False, input=\"What was the purpose of the 'Schrödinger's Garden' project?\", actual_output=\"The Schrödinger's Garden project aimed to explore the potential of quantum field theory to create a stable, self-sustaining microclimate on Mars. The project involved several key innovations, including:\\n\\n**1. Quantum field enhancement:** Researchers proposed increasing the quantum field strength in the quantum garden to enhance the microclimate's effect.\\n\\n**2. Collective root system:** By manipulating the quantum field strength, they created a collective root system that spanned across quantum states.\\n\\n**3. Fruit production:** One of the plants produced a strange fruit that contained seeds in a state that defied biological laws.\\n\\n**4. Risks and limitations:** The project was concerned about potential risks and limitations, such as the plants escaping containment and spreading in unpredictable ways.\\n\\n**5. Machine learning:** Researchers used machine learning techniques to represent and analyze the complex interactions between the plants and the quantum field.\\n\\n**6. Impact and legacy:** The Schrödinger's Garden project paved the way for future research on quantum field theory and its applications to space exploration.\\n\\n**Key takeaways:**\\n\\n* The project explored the potential of quantum field theory to create a stable and self-sustaining microclimate in space.\\n* Researchers achieved significant advancements in understanding the interactions between quantum fields and matter.\\n* The project raised important questions about the risks and limitations associated with using quantum field theory in space.\\n* The project had a significant impact on the field of quantum field theory and inspired future research in the area.\", expected_output=None, context=None, retrieval_context=None, additional_metadata=None)] confident_link=None\n", "\n", "--- Evaluating: What happened during the quantum fluctuation event in greenhouse chamber 7? ---\n", "Response: Sure, here's a comprehensive answer to the question:\n", "\n", "The Quantum Garden experienced a quantum fluct...\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">✨ You're running DeepEval's latest <span style=\"color: #6a00ff; text-decoration-color: #6a00ff\">Answer Relevancy Metric</span>! <span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">(</span><span style=\"color: #374151; text-decoration-color: #374151\">using qwen2.</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">5</span><span style=\"color: #374151; text-decoration-color: #374151\">:latest </span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">(</span><span style=\"color: #374151; text-decoration-color: #374151\">Ollama</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">)</span><span style=\"color: #374151; text-decoration-color: #374151\">, </span><span style=\"color: #374151; text-decoration-color: #374151\">strict</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">False</span><span style=\"color: #374151; text-decoration-color: #374151\">, </span>\n", "<span style=\"color: #374151; text-decoration-color: #374151\">async_mode</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">True</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">)</span><span style=\"color: #374151; text-decoration-color: #374151\">...</span>\n", "</pre>\n"], "text/plain": ["✨ You're running DeepEval's latest \u001b[38;2;106;0;255mAnswer Relevancy Metric\u001b[0m! \u001b[1;38;2;55;65;81m(\u001b[0m\u001b[38;2;55;65;81musing qwen2.\u001b[0m\u001b[1;38;2;55;65;81m5\u001b[0m\u001b[38;2;55;65;81m:latest \u001b[0m\u001b[1;38;2;55;65;81m(\u001b[0m\u001b[38;2;55;65;81mOllama\u001b[0m\u001b[1;38;2;55;65;81m)\u001b[0m\u001b[38;2;55;65;81m, \u001b[0m\u001b[38;2;55;65;81mstrict\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mFalse\u001b[0m\u001b[38;2;55;65;81m, \u001b[0m\n", "\u001b[38;2;55;65;81masync_mode\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mTrue\u001b[0m\u001b[1;38;2;55;65;81m)\u001b[0m\u001b[38;2;55;65;81m...\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4b2e3baaba1d4582913db26d219bdf3a", "version_major": 2, "version_minor": 0}, "text/plain": ["Output()"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======================================================================\n", "\n", "Metrics Summary\n", "\n", "  - ✅ Answer Relevancy (score: 0.75, threshold: 0.7, strict: False, evaluation model: qwen2.5:latest (Ollama), reason: The score is 0.75 because the output includes statements about future applications and actions taken after the event, which are irrelevant to the specific question about what happened during the quantum fluctuation itself., error: None)\n", "\n", "For test case:\n", "\n", "  - input: What happened during the quantum fluctuation event in greenhouse chamber 7?\n", "  - actual output: Sure, here's a comprehensive answer to the question:\n", "\n", "The Quantum Garden experienced a quantum fluctuation event in greenhouse chamber 7, resulting in a localized quantum field that affected the physical constants within the chamber. This event led to the creation of a miniature ecosystem that cycled through four seasons in just 24 hours.\n", "\n", "Within the chamber, the plants were evolving at an unprecedented rate, creating a stable version of the Quantum Garden that could be safely transported to Mars. The first test plot was planted near the Olympus Mons Research Station on Mars in August 2158.\n", "\n", "The Quantum Garden experiment provided valuable insights into the potential of quantum fluctuations to create complex and sustainable ecosystems. This discovery opened up new possibilities for exploring the possibilities of life on other planets and the potential for harnessing quantum phenomena for various applications.\n", "  - expected output: None\n", "  - context: None\n", "  - retrieval context: None\n", "\n", "======================================================================\n", "\n", "Overall Metric Pass Rates\n", "\n", "Answer Relevancy: 100.00% pass rate\n", "\n", "======================================================================\n", "\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "<span style=\"color: #05f58d; text-decoration-color: #05f58d\">✓</span> Tests finished 🎉! Run <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">'deepeval view'</span> to analyze, debug, and save evaluation results on <span style=\"color: #6a00ff; text-decoration-color: #6a00ff\">Confident AI</span>.\n", "\n", "</pre>\n"], "text/plain": ["\n", "\u001b[38;2;5;245;141m✓\u001b[0m Tests finished 🎉! Run \u001b[1;32m'deepeval view'\u001b[0m to analyze, debug, and save evaluation results on \u001b[38;2;106;0;255mConfident AI\u001b[0m.\n", "\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Evaluation complete: test_results=[TestResult(name='test_case_0', success=True, metrics_data=[MetricData(name='Answer Relevancy', threshold=0.7, success=True, score=0.75, reason='The score is 0.75 because the output includes statements about future applications and actions taken after the event, which are irrelevant to the specific question about what happened during the quantum fluctuation itself.', strict_mode=False, evaluation_model='qwen2.5:latest (Ollama)', error=None, evaluation_cost=0.0, verbose_logs='Statements:\\n[\\n    \"The Quantum Garden experienced a quantum fluctuation event in greenhouse chamber 7.\",\\n    \"This event led to the creation of a miniature ecosystem that cycled through four seasons in just 24 hours.\",\\n    \"Within the chamber, the plants were evolving at an unprecedented rate.\",\\n    \"A stable version of the Quantum Garden could be safely transported to Mars.\",\\n    \"The first test plot was planted near the Olympus Mons Research Station on Mars in August 2158.\",\\n    \"The Quantum Garden experiment provided valuable insights into the potential of quantum fluctuations to create complex and sustainable ecosystems.\",\\n    \"This discovery opened up new possibilities for exploring the possibilities of life on other planets.\",\\n    \"It also offered potential for harnessing quantum phenomena for various applications.\"\\n] \\n \\nVerdicts:\\n[\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": null\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": \"This statement directly describes the outcome of the event in greenhouse chamber 7.\"\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": \"This statement provides details about the rapid evolution observed during the event, which is relevant to understanding what happened.\"\\n    },\\n    {\\n        \"verdict\": \"no\",\\n        \"reason\": \"The statement discusses a future application of the Quantum Garden experiment and does not directly address the specific event in greenhouse chamber 7.\"\\n    },\\n    {\\n        \"verdict\": \"no\",\\n        \"reason\": \"This statement describes an action taken after the event, which is not directly related to what happened during the quantum fluctuation itself.\"\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": \"This statement explains the significance of the event in terms of creating complex ecosystems, which is relevant to understanding its impact.\"\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": \"This statement discusses broader implications and applications derived from the event, which are relevant to the overall context.\"\\n    },\\n    {\\n        \"verdict\": \"yes\",\\n        \"reason\": \"This statement provides additional context about the significance of the discovery, which is relevant to understanding its importance.\"\\n    }\\n]')], conversational=False, multimodal=False, input='What happened during the quantum fluctuation event in greenhouse chamber 7?', actual_output=\"Sure, here's a comprehensive answer to the question:\\n\\nThe Quantum Garden experienced a quantum fluctuation event in greenhouse chamber 7, resulting in a localized quantum field that affected the physical constants within the chamber. This event led to the creation of a miniature ecosystem that cycled through four seasons in just 24 hours.\\n\\nWithin the chamber, the plants were evolving at an unprecedented rate, creating a stable version of the Quantum Garden that could be safely transported to Mars. The first test plot was planted near the Olympus Mons Research Station on Mars in August 2158.\\n\\nThe Quantum Garden experiment provided valuable insights into the potential of quantum fluctuations to create complex and sustainable ecosystems. This discovery opened up new possibilities for exploring the possibilities of life on other planets and the potential for harnessing quantum phenomena for various applications.\", expected_output=None, context=None, retrieval_context=None, additional_metadata=None)] confident_link=None\n", "\n", "--- Evaluating: When was the first human settlement established on Mars that didn't require artificial life support?What happened when the quantum field generator was modified? ---\n", "Response: There is no information in the context about the first human settlement established on Mars that did...\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">✨ You're running DeepEval's latest <span style=\"color: #6a00ff; text-decoration-color: #6a00ff\">Answer Relevancy Metric</span>! <span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">(</span><span style=\"color: #374151; text-decoration-color: #374151\">using qwen2.</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">5</span><span style=\"color: #374151; text-decoration-color: #374151\">:latest </span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">(</span><span style=\"color: #374151; text-decoration-color: #374151\">Ollama</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">)</span><span style=\"color: #374151; text-decoration-color: #374151\">, </span><span style=\"color: #374151; text-decoration-color: #374151\">strict</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">False</span><span style=\"color: #374151; text-decoration-color: #374151\">, </span>\n", "<span style=\"color: #374151; text-decoration-color: #374151\">async_mode</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">True</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">)</span><span style=\"color: #374151; text-decoration-color: #374151\">...</span>\n", "</pre>\n"], "text/plain": ["✨ You're running DeepEval's latest \u001b[38;2;106;0;255mAnswer Relevancy Metric\u001b[0m! \u001b[1;38;2;55;65;81m(\u001b[0m\u001b[38;2;55;65;81musing qwen2.\u001b[0m\u001b[1;38;2;55;65;81m5\u001b[0m\u001b[38;2;55;65;81m:latest \u001b[0m\u001b[1;38;2;55;65;81m(\u001b[0m\u001b[38;2;55;65;81mOllama\u001b[0m\u001b[1;38;2;55;65;81m)\u001b[0m\u001b[38;2;55;65;81m, \u001b[0m\u001b[38;2;55;65;81mstrict\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mFalse\u001b[0m\u001b[38;2;55;65;81m, \u001b[0m\n", "\u001b[38;2;55;65;81masync_mode\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mTrue\u001b[0m\u001b[1;38;2;55;65;81m)\u001b[0m\u001b[38;2;55;65;81m...\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ffe581cd1df245e2a34c1b5911de5645", "version_major": 2, "version_minor": 0}, "text/plain": ["Output()"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======================================================================\n", "\n", "Metrics Summary\n", "\n", "  - ❌ Answer Relevancy (score: 0.0, threshold: 0.7, strict: False, evaluation model: qwen2.5:latest (Ollama), reason: The score is 0.00 because both questions asked were completely ignored, with no relevant information provided for either query., error: None)\n", "\n", "For test case:\n", "\n", "  - input: When was the first human settlement established on Mars that didn't require artificial life support?What happened when the quantum field generator was modified?\n", "  - actual output: There is no information in the context about the first human settlement established on Mars that didn't require artificial life support, or about the modification of the quantum field generator.\n", "  - expected output: None\n", "  - context: None\n", "  - retrieval context: None\n", "\n", "======================================================================\n", "\n", "Overall Metric Pass Rates\n", "\n", "Answer Relevancy: 0.00% pass rate\n", "\n", "======================================================================\n", "\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "<span style=\"color: #05f58d; text-decoration-color: #05f58d\">✓</span> Tests finished 🎉! Run <span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">'deepeval view'</span> to analyze, debug, and save evaluation results on <span style=\"color: #6a00ff; text-decoration-color: #6a00ff\">Confident AI</span>.\n", "\n", "</pre>\n"], "text/plain": ["\n", "\u001b[38;2;5;245;141m✓\u001b[0m Tests finished 🎉! Run \u001b[1;32m'deepeval view'\u001b[0m to analyze, debug, and save evaluation results on \u001b[38;2;106;0;255mConfident AI\u001b[0m.\n", "\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Evaluation complete: test_results=[TestResult(name='test_case_0', success=False, metrics_data=[MetricData(name='Answer Relevancy', threshold=0.7, success=False, score=0.0, reason='The score is 0.00 because both questions asked were completely ignored, with no relevant information provided for either query.', strict_mode=False, evaluation_model='qwen2.5:latest (Ollama)', error=None, evaluation_cost=0.0, verbose_logs='Statements:\\n[\\n    \"There is no information in the context about the first human settlement established on Mars that didn\\'t require artificial life support.\",\\n    \"There is no information in the context about the modification of the quantum field generator.\"\\n] \\n \\nVerdicts:\\n[\\n    {\\n        \"verdict\": \"no\",\\n        \"reason\": \"The statement does not address any relevant information to the question about the first human settlement established on Mars.\"\\n    },\\n    {\\n        \"verdict\": \"no\",\\n        \"reason\": \"The statement does not provide any information related to the modification of the quantum field generator mentioned in the second question.\"\\n    }\\n]')], conversational=False, multimodal=False, input=\"When was the first human settlement established on Mars that didn't require artificial life support?What happened when the quantum field generator was modified?\", actual_output=\"There is no information in the context about the first human settlement established on Mars that didn't require artificial life support, or about the modification of the quantum field generator.\", expected_output=None, context=None, retrieval_context=None, additional_metadata=None)] confident_link=None\n"]}], "source": ["from deepeval.metrics import AnswerRelevancyMetric, FaithfulnessMetric, ContextualPrecisionMetric, ContextualRecallMetric\n", "from deepeval.test_case import LLMTestCase\n", "from deepeval import evaluate\n", "from deepeval.models import OllamaModel\n", "import pandas as pd\n", "\n", "# Define the questions to evaluate\n", "questions = [\n", "    \"What is the Quantum Fern and what makes it so special?\",\n", "    \"Who is <PERSON>. <PERSON>?\",\n", "    \"What was Dr. <PERSON><PERSON><PERSON> concerned about?\",\n", "    \"How did the Quantum Garden impact Mars colonization?\",\n", "    \"What did <PERSON> suggest to enhance the communication between Quantum Ferns?\", \n", "    \"What unusual behavior did <PERSON> notice in greenhouse chamber 7?\",\n", "    \"What was the purpose of the 'Schr<PERSON>dinger's Garden' project?\",\n", "    \"What happened during the quantum fluctuation event in greenhouse chamber 7?\",\n", "    \"When was the first human settlement established on Mars that didn't require artificial life support?\"\n", "    \"What happened when the quantum field generator was modified?\"\n", "]\n", "\n", "# Initialize the metrics once (reuse for all evaluations)\n", "metrics = [\n", "    AnswerRelevancyMetric(\n", "        model=OllamaModel(model=\"qwen2.5:latest\", base_url=\"http://localhost:11434\"),\n", "        threshold=0.7, \n", "        include_reason=True\n", "    )\n", "]\n", "\n", "# Dictionary to store results\n", "evaluation_results = {}\n", "\n", "# Loop through each question\n", "for question in questions:\n", "    print(f\"\\n--- Evaluating: {question} ---\")\n", "    \n", "    # Get response from RAG agent\n", "    response = strategic_multi_query_rag(question)  # You can replace with any agent\n", "    print(f\"Response: {response[:100]}...\")  # Print first 100 chars\n", "    \n", "    # Create test case\n", "    test_case = LLMTestCase(\n", "        input=question,\n", "        actual_output=response,\n", "    )\n", "    \n", "    # Run evaluation\n", "    result = evaluate(test_cases=[test_case], metrics=metrics)\n", "    \n", "    # Store results\n", "    evaluation_results[question] = {\n", "        \"response\": response,\n", "        \"metrics\": result\n", "    }\n", "    \n", "    # Print summary\n", "    print(f\"Evaluation complete: {result}\")\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 5}