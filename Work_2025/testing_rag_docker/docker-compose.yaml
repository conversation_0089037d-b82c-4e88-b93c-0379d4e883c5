services:
  ollama:
    image: ollama/ollama
    container_name: ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    # GPU configuration is now optional
    # Uncomment these lines if you have an NVIDIA GPU and Container Toolkit
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

  rag_app:
    build:
      context: ./app
      dockerfile: Dockerfile
    container_name: rag_app
    ports:
      - "8501:8501"
    environment:
      - OLLAMA=http://ollama:11434
    depends_on:
      - ollama
    volumes:
      - ./data:/app/data

volumes:
  ollama_data:
