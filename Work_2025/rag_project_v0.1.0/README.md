# Strategic RAG System

A modular, configurable Retrieval-Augmented Generation (RAG) system with strategic multi-query capabilities.

## Features

- Strategic multi-query RAG for improved retrieval
- Modular architecture for easy extension
- Document ingestion for PDFs and text files
- Configurable models and parameters
- Streamlit UI for interactive querying
- Docker containerization for easy deployment

## Project Structure

```
generative_rag_project/
├── config/                  # Configuration directory
│   ├── model_config.yaml    # Model-specific configurations
│   └── prompt_templates.yaml # Prompt templates
│
├── src/                     # Source code
│   ├── llm/                # LLM clients
│   ├── rag/                # RAG components
│   ├── document_processing/ # Document processing
│   ├── utils/             # Utility functions
│   └── ui/                # UI components
│
├── data/                   # Data directory
│   ├── uploads/           # Uploaded documents
│   └── vector_stores/     # Vector store data
│
├── examples/              # Example implementations
├── notebooks/            # Jupyter notebooks
├── docker/               # Docker configuration
├── scripts/              # Utility scripts
│
└── requirements.txt       # Python dependencies
```

## Getting Started

### Prerequisites

- Docker and Docker Compose

#### Optional for GPU Acceleration
- NVIDIA GPU with CUDA support
- NVIDIA Container Toolkit installed

### Setup

Make sure to run this: aws sso login --profile charter-intern

1. Run the setup script to download required models:

```bash
chmod +x scripts/download_models.sh
./scripts/download_models.sh
```

2. Start the application:

```bash
docker compose -f docker/docker-compose.yaml up --build
```

3. Access the UI at o

### Data Ingestion

You can ingest documents in two ways:

1. Through the UI: Upload files using the sidebar uploader
2. Using the ingestion script:

```bash
# Place your documents in the data/documents folder
mkdir -p data/documents
# Copy your documents to data/documents

# Run the ingestion script
python scripts/ingest.py --dir ./data/documents
```

## Configuration

The system is configured through YAML files in the `config` directory:

- `model_config.yaml`: Configure LLM and embedding models
- `prompt_templates.yaml`: Define prompt templates for RAG strategies

## Architecture

### RAG Strategies

The system implements a strategic multi-query RAG approach that:

1. Generates multiple search strategies for a given question
2. Executes searches with different query formulations
3. Combines and synthesizes the results for a comprehensive answer

### Components

- **LLM Client**: Interfaces with Ollama for text generation and embeddings
- **Document Processing**: Handles loading, splitting, and embedding documents
- **Retriever**: Manages the vector store and retrieves relevant documents
- **RAG Strategies**: Implements different RAG approaches
- **UI**: Streamlit interface for user interaction

## Development

### Adding New RAG Strategies

To add a new RAG strategy:

1. Create a new class in `src/rag/strategies.py`
2. Implement the required methods
3. Update the UI to use the new strategy

### Customizing Prompts

Edit the prompt templates in `config/prompt_templates.yaml` to customize the behavior of the RAG system.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Behavior

docker-compose -f docker/docker-compose.yaml up --build

docker-compose -f docker/docker-compose.yaml down 

Should run the ingestion script from the s3 bucket once
