"""Entry point for the RAG application."""
import os
import streamlit.web.cli as stcli
import sys

if __name__ == "__main__":
    # Set the working directory to the project root
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # Add the current directory to the Python path
    sys.path.insert(0, os.path.abspath("."))
    
    # Run the Streamlit app
    sys.argv = ["streamlit", "run", "src/ui/app.py", "--server.port=8501", "--server.address=0.0.0.0"]
    sys.exit(stcli.main())