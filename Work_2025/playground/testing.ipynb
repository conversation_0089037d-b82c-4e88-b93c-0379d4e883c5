#Load environment variables FIRST
import os
from dotenv import load_dotenv

# Load environment variables before any other imports
load_dotenv()

from langchain_ollama import ChatOllama, OllamaEmbeddings
from langchain_chroma import Chroma
from langchain_community.document_loaders import PyPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.prompts import Chat<PERSON>romptTemplate
from langchain_core.output_parsers import StrOutputParser
from langsmith import traceable


def initialize_models():
    llm = ChatOllama(
        base_url="http://localhost:11434",
        model = "gemma:2b",
        temperature=0.5,
        max_tokens = 500
    )

    embedding_model = OllamaEmbeddings(
        base_url="http://localhost:11434",
        model = "gemma:2b",
    )
    return llm, embedding_model
llm, embedding_model = initialize_models()


@traceable
def load_pdf(pdf):
    loader = PyPDFLoader(pdf)
    documents.extend(loader.load())

@traceable
def split_and_store(documents):
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
    chunks = text_splitter.split_documents(documents)
    vector_store = Chroma.from_documents(chunks, embedding_model, persist_directory="./chroma_db_gemma")
    return vector_store


pdf1 = "./attention.pdf"

pdfFiles = [pdf1]

documents = []
for pdf in pdfFiles:
    load_pdf(pdf)

vector_store = split_and_store(documents)



@traceable
def get_relevant_docs(query, k=1):
    docs = vector_store.similarity_search_with_score(query, k=k)
    context_with_scores = []
    for doc, score in docs:
        context_with_scores.append(f"[Score: {score:.4f}]\n{doc.page_content}")
    
    # Join all chunks with their scores
    context = "\n\n" + "\n\n---\n\n".join(context_with_scores)
    return context

@traceable
def ask_question(question,chain):
    context = get_relevant_docs(question)
    print("CONTEXT:")
    print(context)
    response = chain.invoke({"context": context, "question": question})
    return response
#retriever = vector_store.as_retriever(search_type ="similarity", search_kwargs={"k":1 })

#print(retriever.get_relevant_documents("What is the attention mechanism?"))
#retrieved_docs = retriever.get_relevant_documents("What is multi-head attention?")
#context = "\n\n".join([d.page_content for d in retrieved_docs])
#print(context)


prompt_template = ChatPromptTemplate.from_template(
    """
    You are an AI Assistant that ONLY answers based on the provided context. 
    
    IMPORTANT RULES:
    1. NEVER make up information that is not explicitly stated in the context
    2. If the context doesn't contain the answer, say "I don't have enough information to answer this question"
    3. Do not use any prior knowledge about people, places, or events
    4. Stick strictly to the facts presented in the context
    5. Do not elaborate beyond what is directly supported by the context
    
    Also, summarize the response in MD format
    
    "context: {context} \n\n"
    "question: {question} \n\n"
    "Answer: (using ONLY facts from the context)
    
    """
)

chain = prompt_template | llm | StrOutputParser()

question = "How does the Transformer reduce the number of operations needed to relate distant positions compared to ConvS2S and ByteNet?"
#How does the Transformer reduce the number of operations needed to relate distant positions compared to ConvS2S and ByteNet?


response = ask_question(question, chain)
print("ASNWER:")
print(response)




@traceable
def agentic_rag(question: str, max_iterations: int = 3):
    """
    Agentic RAG that can perform multi-step reasoning and refinement
    """
    conversation_history = []
    current_question = question
    
    for iteration in range(max_iterations):
        print(f"\n--- Iteration {iteration + 1} ---")
        
        # Step 1: Analyze the question and decide on search strategy
        analysis_prompt = f"""
        Analyze this question and determine the best search strategy:
        Question: {current_question}
        
        Previous context: {conversation_history}
        
        What specific terms or concepts should I search for? 
        Should I break this into sub-questions?
        Provide 2-3 specific search terms or phrases that would find the most relevant information.
        Format your response as: SEARCH_TERMS: term1, term2, term3
        """
        
        search_strategy = llm.invoke(analysis_prompt).content
        print(f"Search Strategy: {search_strategy}")
        if "SEARCH_TERMS:" in search_strategy:
            search_terms = search_strategy.split("SEARCH_TERMS:")[1].strip()
            search_queries = [term.strip() for term in search_terms.split(",")]
        else:
            # Fallback to original question if parsing fails
            search_queries = [current_question]
        
        # Step 2: Search for information
        print(f"Executing searches for: {search_queries}")
        
        # Step 3: Execute the search strategy
        all_contexts = []
        for search_query in search_queries:
            context = get_relevant_docs(search_query, k=2)  # Fewer docs per query
            all_contexts.append(f"Search: '{search_query}'\nResults:\n{context}")
        
        combined_context = "\n\n---\n\n".join(all_contexts)
        
        # Step 3: Evaluate if we have enough information
        evaluation_prompt = f"""
        Question: {current_question}
        Retrieved Context: {combined_context}
        
        Do I have enough information to answer this question completely? 
        If not, what additional information do I need?
        Respond with either "SUFFICIENT" or "NEED_MORE: [specific information needed]"
        """
        
        evaluation = llm.invoke(evaluation_prompt).content
        print(f"Evaluation: {evaluation}")
        
        if evaluation.startswith("SUFFICIENT"):
            # Generate final answer
            final_answer = chain.invoke({"context": context, "question": current_question})
            return final_answer
        elif evaluation.startswith("NEED_MORE"):
            # Extract what we need and refine the search
            needed_info = evaluation.split("NEED_MORE:")[1].strip()
            current_question = f"{current_question} Specifically: {needed_info}"
            conversation_history.append(f"Iteration {iteration + 1}: {context[:200]}...")
        
    # If we've exhausted iterations, provide best answer available
    final_context = get_relevant_docs(question, k=7)
    return chain.invoke({"context": final_context, "question": question})

@traceable
def strategic_multi_query_rag(question: str):
    """
    Agent that generates multiple strategic search queries and combines results
    """
    
    # Step 1: Generate multiple search strategies
    strategy_prompt = f"""
    For this question: "{question}"
    
    Generate 3 different search strategies to find comprehensive information if you deem the strategy necessary (if you think no technical strategy is needed just put N/A):
    1. A broad search to understand the general concept
    2. A specific technical search for detailed mechanisms  
    3. A comparative search to understand relationships/differences
    
    Format as:
    BROAD: [search terms]
    TECHNICAL: [search terms]  
    COMPARATIVE: [search terms]
    """
    
    strategies = llm.invoke(strategy_prompt).content
    #print(f"Search Strategies:\n{strategies}")
    
    # Step 2: Extract and execute each strategy
    search_results = {}
    
    for strategy_type in ["BROAD", "TECHNICAL", "COMPARATIVE"]:
        if f"{strategy_type}:" in strategies:
            search_terms = strategies.split(f"{strategy_type}:")[1].split("\n")[0].strip()
            #print(f"\nExecuting {strategy_type} search: {search_terms}")
            
            context = get_relevant_docs(search_terms, k=3)
            search_results[strategy_type] = {
                "query": search_terms,
                "context": context
            }
    
    # Step 3: Synthesize all search results
    combined_context = ""
    for strategy_type, result in search_results.items():
        combined_context += f"\n\n=== {strategy_type} SEARCH ===\n"
        combined_context += f"Query: {result['query']}\n"
        combined_context += f"Results: {result['context']}\n"
    
    # Step 4: Generate comprehensive answer
    synthesis_prompt = f"""
    Question: {question}
    
    I've gathered information using multiple search strategies:
    {combined_context}
    
    Provide a comprehensive answer that synthesizes information from all searches.
    """
    
    final_answer = llm.invoke(synthesis_prompt).content
    return final_answer

@traceable
def self_reflective_rag(question: str):
    """
    RAG agent that reflects on its own answers and improves them
    """
    
    # Initial answer
    initial_context = get_relevant_docs(question, k=3)
    initial_answer = chain.invoke({"context": initial_context, "question": question})
    
    # Self-reflection
    reflection_prompt = f"""
    I just answered this question: {question}
    My answer was: {initial_answer}
    Based on context: {initial_context[:300]}...
    
    Critically evaluate my answer:
    1. Is it complete and accurate?
    2. What important aspects might be missing?
    3. Should I search for additional information?
    4. Rate the answer quality (1-10) and explain why.
    
    Provide specific suggestions for improvement.
    """
    
    reflection = llm.invoke(reflection_prompt).content
    print(f"Self-Reflection: {reflection}")
    
    # If reflection suggests improvement, search for more info
    if "search for additional" in reflection.lower() or any(word in reflection.lower() for word in ["incomplete", "missing", "need more"]):
        # Extract key terms for additional search
        additional_search_prompt = f"""
        Based on this reflection: {reflection}
        What specific terms should I search for to improve my answer to: {question}
        Provide 2-3 specific search terms.
        """
        
        search_terms = llm.invoke(additional_search_prompt).content
        print(f"Additional search terms: {search_terms}")
        
        # Search with new terms
        additional_context = get_relevant_docs(search_terms, k=3)
        
        # Generate improved answer
        combined_context = initial_context + "\n\n--- Additional Information ---\n\n" + additional_context
        improved_answer = chain.invoke({"context": combined_context, "question": question})
        
        return {
            "initial_answer": initial_answer,
            "reflection": reflection,
            "improved_answer": improved_answer,
            "final_answer": improved_answer
        }
    
    return {
        "initial_answer": initial_answer,
        "reflection": reflection,
        "final_answer": initial_answer
    }

@traceable
def decomposition_agent(complex_question: str):
    """
    Agent that breaks down complex questions into simpler sub-questions
    """
    
    # Decompose the question
    decomposition_prompt = f"""
    Break down this complex question into 2-4 simpler sub-questions that, when answered together, 
    would fully address the original question:
    
    Complex Question: {complex_question}
    
    Provide sub-questions as a numbered list.
    """
    
    decomposition = llm.invoke(decomposition_prompt).content
    print(f"Question Decomposition:\n{decomposition}")
    
    # Extract sub-questions (simple parsing)
    sub_questions = []
    for line in decomposition.split('\n'):
        if line.strip() and (line.strip()[0].isdigit() or line.strip().startswith('-')):
            sub_questions.append(line.strip().split('.', 1)[-1].strip())
    
    # Answer each sub-question
    sub_answers = {}
    for i, sub_q in enumerate(sub_questions, 1):
        print(f"\n--- Answering Sub-question {i}: {sub_q} ---")
        context = get_relevant_docs(sub_q, k=3)
        answer = chain.invoke({"context": context, "question": sub_q})
        sub_answers[sub_q] = answer
        print(f"Answer: {answer}")
    
    # Synthesize final answer
    synthesis_prompt = f"""
    Original complex question: {complex_question}
    
    Sub-questions and their answers:
    {chr(10).join([f"Q: {q}A: {a}" for q, a in sub_answers.items()])}
    
    Now synthesize these sub-answers into a comprehensive response to the original question."""
    
    final_answer = llm.invoke(synthesis_prompt).content
    
    return {
        "original_question": complex_question,
        "sub_questions": sub_questions,
        "sub_answers": sub_answers,
        "final_answer": final_answer
    }

# Test the agentic approaches
question = "How does multi-head attention improve upon single attention mechanisms and what are its computational advantages?"

# Try different agentic approaches
print("=== Multi-Step Reasoning Agent ===")
result1 = agentic_rag(question)
print(f"Final Answer: {result1}")

print("\n=== Strategic Multi-Query ===")
result5 = strategic_multi_query_rag(question)
print(f"Final Answer: {result5}")

print("\n=== Self-Reflective Agent ===")
result2 = self_reflective_rag(question)

print("\n=== Question Decomposition Agent ===")
result3 = decomposition_agent(question)
print("Complete decomposition result:")
for key, value in result3.items():
    print(f"\n{key.upper()}:")
    print(value)


# Load the dataset
import pandas as pd
from langchain_core.documents import Document

# Load your dataset
df = pd.read_csv('./story_dataset.csv')

# Convert dataframe rows to documents
documents = []
for i, row in df.iterrows():
    # Create a document with the query and answer
    doc = Document(
        page_content=f"Question: {row['query']}\nAnswer: {row['answer']}",
        metadata={
            "source": "dataset.csv",
            "row_id": i,
            "type": "qa_pair"
        }
    )
    documents.append(doc)

print(f"Created {len(documents)} documents from storay_dataset.csv")

# Add documents to the vector store
@traceable
def add_documents_to_vectorstore(docs):
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
    chunks = text_splitter.split_documents(docs)
    print(f"Split into {len(chunks)} chunks")
    
    # Add to existing vector store
    vector_store.add_documents(chunks)
    print("Documents added to vector store")
    return len(chunks)

# Add the documents to the vector store
num_chunks = add_documents_to_vectorstore(documents)
print(f"Added {num_chunks} chunks to vector store")

# Now run your evaluation with the agents
# They will be able to retrieve information from the dataset

with open("story.txt", "r") as file:
    story_text = file.read()
    print(f"Loaded story.txt: {len(story_text)} characters")
    
# Create document from story
story_doc = Document(
    page_content=story_text,
    metadata={"source": "story.txt", "type": "story"}
)

# Split document into chunks
text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
chunks = text_splitter.split_documents([story_doc])
print(f"Split into {len(chunks)} chunks")

# Add to vector store
vector_store.add_documents(chunks)
print(f"Added {len(chunks)} chunks to vector store at ./chroma_db_gemma")

# Test retrieval
test_query = "What is special about the Quantum Fern?"
results = vector_store.similarity_search_with_score(test_query, k=1)
doc, score = results[0]
print(f"\nTest Query: '{test_query}'")
print(f"Retrieved Document Score: {score:.4f}")
print(f"Content: {doc.page_content[:200]}...")


print("\n=== STEP 3: DEFINING AGENTS ===")
agents = {
    #"multi_step": agentic_rag,
    #"decomposition": lambda q: decomposition_agent(q)["final_answer"],
    #"self_reflective": lambda q: self_reflective_rag(q)["final_answer"],
    "strategic": strategic_multi_query_rag
}

from deepeval.metrics import AnswerRelevancyMetric, FaithfulnessMetric, ContextualPrecisionMetric, ContextualRecallMetric
from deepeval.test_case import LLMTestCase
from deepeval import evaluate
from deepeval.models import OllamaModel
import pandas as pd

# Define the questions to evaluate
questions = [
    "What is the Quantum Fern and what makes it so special?",
    "Who is Dr. Elena Reyes?",
    "What was Dr. Yoshiko Tanaka concerned about?",
    "How did the Quantum Garden impact Mars colonization?",
    "What did Marcus Chen suggest to enhance the communication between Quantum Ferns?", 
    "What unusual behavior did Elena notice in greenhouse chamber 7?",
    "What was the purpose of the 'Schrödinger's Garden' project?",
    "What happened during the quantum fluctuation event in greenhouse chamber 7?",
    "When was the first human settlement established on Mars that didn't require artificial life support?"
    "What happened when the quantum field generator was modified?"
]

# Initialize the metrics once (reuse for all evaluations)
metrics = [
    AnswerRelevancyMetric(
        model=OllamaModel(model="qwen2.5:latest", base_url="http://localhost:11434"),
        threshold=0.7, 
        include_reason=True
    )
]

# Dictionary to store results
evaluation_results = {}

# Loop through each question
for question in questions:
    print(f"\n--- Evaluating: {question} ---")
    
    # Get response from RAG agent
    response = strategic_multi_query_rag(question)  # You can replace with any agent
    print(f"Response: {response[:100]}...")  # Print first 100 chars
    
    # Create test case
    test_case = LLMTestCase(
        input=question,
        actual_output=response,
    )
    
    # Run evaluation
    result = evaluate(test_cases=[test_case], metrics=metrics)
    
    # Store results
    evaluation_results[question] = {
        "response": response,
        "metrics": result
    }
    
    # Print summary
    print(f"Evaluation complete: {result}")

