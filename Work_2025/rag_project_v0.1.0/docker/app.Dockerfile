FROM python:3.10-slim

WORKDIR /app

# Install curl for healthchecks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# AWS CLI for SSO support

RUN pip install --no-cache-dir -r requirements.txt

# Copy the application code
COPY . .

# Expose the Streamlit port
EXPOSE 8501

# Set the environment variable explicitly with protocol
ENV OLLAMA_URL=http://ollama2:11434

# Run the check_env script before starting the app
#CMD ["sh", "-c", "python scripts/check_env.py && python -m streamlit run src/ui/app.py --server.port=8501 --server.address=0.0.0.0"]
CMD ["sh", "-c", "python scripts/check_env.py && streamlit run src/ui/app.py --server.port=8501 --server.address=0.0.0.0 --server.runOnSave=true"]
