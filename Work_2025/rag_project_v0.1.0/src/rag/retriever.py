"""Document retrieval functionality."""
import os
from typing import List, Dict, Any, Tu<PERSON>, Optional

from langchain_chroma import Chroma
from langchain_ollama import OllamaEmbeddings
import yaml

class DocumentRetriever:
    """Retrieves relevant documents from a vector store."""
    
    def __init__(self, config_path: str = "config/model_config.yaml"):
        """Initialize the document retriever."""
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Load embedding model
        embedding_config = config.get('embeddings', {})
        
        # Get base_url and ensure it has a protocol
        base_url = embedding_config.get('base_url', os.environ.get("OLLAMA_URL", "http://localhost:11434"))
        if not base_url.startswith(('http://', 'https://')):
            base_url = f"http://{base_url}"
        
        # Print for debugging
        print(f"DocumentRetriever initialized with base_url: {base_url}")
        
        self.embedding_model = OllamaEmbeddings(
            base_url=base_url,
            model=embedding_config.get('model', "gemma:2b"),
        )
        
        # Load vector store
        vectorstore_config = config.get('vectorstore', {})
        persist_directory = vectorstore_config.get('persist_directory', "./data/vector_stores/chroma_db")
        
        # Create directory if it doesn't exist
        os.makedirs(persist_directory, exist_ok=True)
        
        self.vector_store = Chroma(
            persist_directory=persist_directory,
            embedding_function=self.embedding_model
        )
    
    def get_relevant_documents(self, query: str, k: int = 3) -> Tuple[List[Dict[str, Any]], str]:
        """
        Retrieve relevant documents for a query.
        
        Args:
            query: The search query
            k: Number of documents to retrieve
            
        Returns:
            Tuple of (list of documents with scores, formatted context string)
        """
        docs = self.vector_store.similarity_search_with_score(query, k=k)
        
        # Format documents with scores
        docs_with_scores = []
        context_with_scores = []
        
        for doc, score in docs:
            docs_with_scores.append({
                "content": doc.page_content,
                "metadata": doc.metadata,
                "score": score
            })
            context_with_scores.append(f"[Score: {score:.4f}]\n{doc.page_content}")
        
        # Join all chunks with their scores
        context = "\n\n" + "\n\n---\n\n".join(context_with_scores)
        
        return docs_with_scores, context
