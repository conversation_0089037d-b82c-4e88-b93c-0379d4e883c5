"""RAG strategies implementation."""
from typing import Dict, Any, List, Optional
import yaml
import os

from src.llm.ollama_client import OllamaClient
from src.rag.retriever import DocumentRetriever

class StrategicMultiQueryRAG:
    """Strategic multi-query RAG implementation."""
    
    def __init__(self, llm_client=None, retriever=None, config_path: str = "config/model_config.yaml"):
        """Initialize the strategic RAG system."""
        # Load LLM client if not provided
        if llm_client is None:
            self.llm_client = OllamaClient.load_ollama_from_config(config_path)
        else:
            self.llm_client = llm_client
        
        # Load document retriever if not provided
        if retriever is None:
            self.retriever = DocumentRetriever(config_path)
        else:
            self.retriever = retriever
        
        # Load prompt templates
        with open("config/prompt_templates.yaml", 'r') as f:
            templates = yaml.safe_load(f)
        
        self.templates = templates.get('rag', {})
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from file."""
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
    
    def generate_search_strategies(self, question: str) -> Dict[str, str]:
        """Generate multiple search strategies for a question."""
        strategy_prompt = self.templates.get('strategy_generation', '').format(question=question)
        strategies_text = self.llm_client.generate(strategy_prompt)
        
        # Parse strategies
        strategies = {}
        for strategy_type in ["BROAD", "TECHNICAL", "COMPARATIVE"]:
            if f"{strategy_type}:" in strategies_text:
                search_terms = strategies_text.split(f"{strategy_type}:")[1].split("\n")[0].strip()
                strategies[strategy_type] = search_terms
        
        return strategies
    
    def execute_search_strategies(self, strategies: Dict[str, str], k: int = 3) -> Dict[str, Dict[str, Any]]:
        """Execute search for each strategy."""
        search_results = {}
        
        for strategy_type, search_terms in strategies.items():
            _, context = self.retriever.get_relevant_documents(search_terms, k=k)
            search_results[strategy_type] = {
                "query": search_terms,
                "context": context
            }
        
        return search_results
    
    def synthesize_results(self, question: str, search_results: Dict[str, Dict[str, Any]]) -> str:
        """Synthesize results from multiple searches."""
        # Combine contexts
        combined_context = ""
        for strategy_type, result in search_results.items():
            combined_context += f"\n\n=== {strategy_type} SEARCH ===\n"
            combined_context += f"Query: {result['query']}\n"
            combined_context += f"Results: {result['context']}\n"
        
        # Generate synthesis
        synthesis_prompt = self.templates.get('synthesis', '').format(
            question=question,
            combined_context=combined_context
        )
        
        return self.llm_client.generate(synthesis_prompt)
    
    def query(self, question: str, k: int = 3) -> Dict[str, Any]:
        """
        Execute full strategic RAG pipeline.
        
        Args:
            question: User question
            k: Number of documents to retrieve per strategy
            
        Returns:
            Dictionary with final answer and intermediate results
        """
        # Step 1: Generate search strategies
        strategies = self.generate_search_strategies(question)
        
        # Step 2: Execute searches
        search_results = self.execute_search_strategies(strategies, k)
        
        # Step 3: Synthesize results
        final_answer = self.synthesize_results(question, search_results)
        
        return {
            "question": question,
            "strategies": strategies,
            "search_results": search_results,
            "final_answer": final_answer
        }


class AgenticSelfReflectiveRAG:
    """Agentic self-reflective RAG implementation with iterative improvement."""

    def __init__(self, llm_client=None, retriever=None, config_path: str = "config/model_config.yaml"):
        """Initialize the agentic self-reflective RAG system."""
        # Load LLM client if not provided
        if llm_client is None:
            self.llm_client = OllamaClient.load_ollama_from_config(config_path)
        else:
            self.llm_client = llm_client

        # Load document retriever if not provided
        if retriever is None:
            self.retriever = DocumentRetriever(config_path)
        else:
            self.retriever = retriever

        # Load prompt templates
        with open("config/prompt_templates.yaml", 'r') as f:
            templates = yaml.safe_load(f)

        self.templates = templates.get('agentic_rag', {})
        self.max_iterations = 3  # Maximum reflection iterations

    def generate_initial_answer(self, question: str, k: int = 5) -> Dict[str, Any]:
        """Generate initial answer using basic RAG."""
        # Retrieve relevant documents
        documents, context = self.retriever.get_relevant_documents(question, k=k)

        # Generate initial answer
        initial_prompt = self.templates.get('initial_answer', '').format(
            question=question,
            context=context
        )

        initial_answer = self.llm_client.generate(initial_prompt)

        return {
            "answer": initial_answer,
            "context": context,
            "documents": documents
        }

    def self_reflect(self, question: str, answer: str, context: str) -> Dict[str, Any]:
        """Perform self-reflection on the generated answer."""
        reflection_prompt = self.templates.get('self_reflection', '').format(
            question=question,
            answer=answer,
            context=context
        )

        reflection = self.llm_client.generate(reflection_prompt)

        # Parse reflection to extract issues and improvement suggestions
        issues = []
        improvements = []

        if "ISSUES:" in reflection:
            issues_text = reflection.split("ISSUES:")[1].split("IMPROVEMENTS:")[0].strip()
            issues = [issue.strip() for issue in issues_text.split('\n') if issue.strip()]

        if "IMPROVEMENTS:" in reflection:
            improvements_text = reflection.split("IMPROVEMENTS:")[1].strip()
            improvements = [imp.strip() for imp in improvements_text.split('\n') if imp.strip()]

        return {
            "reflection": reflection,
            "issues": issues,
            "improvements": improvements,
            "needs_improvement": len(issues) > 0
        }

    def generate_refined_queries(self, question: str, issues: List[str], improvements: List[str]) -> List[str]:
        """Generate refined search queries based on reflection."""
        refinement_prompt = self.templates.get('query_refinement', '').format(
            original_question=question,
            issues='\n'.join(issues),
            improvements='\n'.join(improvements)
        )

        refined_queries_text = self.llm_client.generate(refinement_prompt)

        # Extract queries
        queries = []
        for line in refined_queries_text.split('\n'):
            if line.strip() and not line.startswith('#'):
                queries.append(line.strip())

        return queries[:3]  # Limit to 3 refined queries

    def improve_answer(self, question: str, original_answer: str, original_context: str,
                      refined_queries: List[str], k: int = 3) -> str:
        """Improve the answer using refined queries and additional context."""
        # Gather additional context from refined queries
        additional_contexts = []
        for query in refined_queries:
            _, context = self.retriever.get_relevant_documents(query, k=k)
            additional_contexts.append(context)

        # Combine all contexts
        combined_context = original_context + "\n\n=== ADDITIONAL CONTEXT ===\n" + \
                          "\n\n".join(additional_contexts)

        # Generate improved answer
        improvement_prompt = self.templates.get('answer_improvement', '').format(
            question=question,
            original_answer=original_answer,
            combined_context=combined_context
        )

        improved_answer = self.llm_client.generate(improvement_prompt)
        return improved_answer

    def query(self, question: str, k: int = 5) -> Dict[str, Any]:
        """
        Execute full agentic self-reflective RAG pipeline.

        Args:
            question: User question
            k: Number of documents to retrieve per query

        Returns:
            Dictionary with final answer and reflection history
        """
        reflection_history = []

        # Step 1: Generate initial answer
        initial_result = self.generate_initial_answer(question, k)
        current_answer = initial_result["answer"]
        current_context = initial_result["context"]

        # Step 2: Iterative self-reflection and improvement
        for iteration in range(self.max_iterations):
            # Self-reflect on current answer
            reflection_result = self.self_reflect(question, current_answer, current_context)
            reflection_history.append({
                "iteration": iteration + 1,
                "reflection": reflection_result
            })

            # If no issues found, we're done
            if not reflection_result["needs_improvement"]:
                break

            # Generate refined queries based on reflection
            refined_queries = self.generate_refined_queries(
                question,
                reflection_result["issues"],
                reflection_result["improvements"]
            )

            # Improve answer using refined queries
            current_answer = self.improve_answer(
                question,
                current_answer,
                current_context,
                refined_queries,
                k
            )

        return {
            "question": question,
            "initial_answer": initial_result["answer"],
            "final_answer": current_answer,
            "reflection_history": reflection_history,
            "total_iterations": len(reflection_history)
        }
