import streamlit as st
import os
from dotenv import load_dotenv
from langchain_ollama import ChatOllama, OllamaEmbeddings
from langchain_chroma import Chroma
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.prompts import Chat<PERSON>romptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_community.document_loaders import PyPDFLoader
from langchain_community.document_loaders import TextLoader

# Load environment variables
load_dotenv()

# Set page config
st.set_page_config(page_title="RAG Agent", page_icon="🤖", layout="centered")

# Initialize models
@st.cache_resource
def initialize_models():
    llm = ChatOllama(
        base_url=os.environ.get("OLLAMA", "http://localhost:11434"),
        model="gemma:2b",
        temperature=0.5,
        max_tokens=500
    )

    embedding_model = OllamaEmbeddings(
        base_url=os.environ.get("OLLAMA", "http://localhost:11434"),
        model="gemma:2b",
    )
    return llm, embedding_model

llm, embedding_model = initialize_models()

# Initialize vector store
@st.cache_resource
def get_vector_store():
    try:
        # Create data directories if they don't exist
        os.makedirs("./data/chroma_db", exist_ok=True)
        
        vector_store = Chroma(
            persist_directory="./data/chroma_db",
            embedding_function=embedding_model
        )
        return vector_store
    except Exception as e:
        st.error(f"Error loading vector store: {e}")
        return None

vector_store = get_vector_store()

# Define the prompt template
prompt_template = ChatPromptTemplate.from_template(
    """
    You are an AI Assistant that ONLY answers based on the provided context. 
    
    IMPORTANT RULES:
    1. NEVER make up information that is not explicitly stated in the context
    2. If the context doesn't contain the answer, say "I don't have enough information to answer this question"
    3. Do not use any prior knowledge about people, places, or events
    4. Stick strictly to the facts presented in the context
    5. Do not elaborate beyond what is directly supported by the context
    
    "context: {context} \n\n"
    "question: {question} \n\n"
    "Answer: (using ONLY facts from the context)
    """
)

chain = prompt_template | llm | StrOutputParser()

# Get relevant documents
def get_relevant_docs(query, k=1):
    if vector_store is None:
        return "Vector store not initialized."
    
    docs = vector_store.similarity_search_with_score(query, k=k)
    context_with_scores = []
    for doc, score in docs:
        context_with_scores.append(f"[Score: {score:.4f}]\n{doc.page_content}")
    
    # Join all chunks with their scores
    context = "\n\n" + "\n\n---\n\n".join(context_with_scores)
    return context

# Strategic multi-query RAG
def strategic_multi_query_rag(question: str):
    """
    Agent that generates multiple strategic search queries and combines results
    """
    
    # Step 1: Generate multiple search strategies
    strategy_prompt = f"""
    For this question: "{question}"
    
    Generate 3 different search strategies to find comprehensive information:
    1. A broad search to understand the general concept
    2. A specific technical search for detailed mechanisms  
    3. A comparative search to understand relationships/differences
    
    Format as:
    BROAD: [search terms]
    TECHNICAL: [search terms]  
    COMPARATIVE: [search terms]
    """
    
    strategies = llm.invoke(strategy_prompt).content
    
    # Step 2: Extract and execute each strategy
    search_results = {}
    
    for strategy_type in ["BROAD", "TECHNICAL", "COMPARATIVE"]:
        if f"{strategy_type}:" in strategies:
            search_terms = strategies.split(f"{strategy_type}:")[1].split("\n")[0].strip()
            
            context = get_relevant_docs(search_terms, k=3)
            search_results[strategy_type] = {
                "query": search_terms,
                "context": context
            }
    
    # Step 3: Synthesize all search results
    combined_context = ""
    for strategy_type, result in search_results.items():
        combined_context += f"\n\n=== {strategy_type} SEARCH ===\n"
        combined_context += f"Query: {result['query']}\n"
        combined_context += f"Results: {result['context']}\n"
    
    # Step 4: Generate comprehensive answer
    synthesis_prompt = f"""
    Question: {question}
    
    I've gathered information using multiple search strategies:
    {combined_context}
    
    Provide a comprehensive answer that synthesizes information from all searches.
    """
    
    final_answer = llm.invoke(synthesis_prompt).content
    return final_answer, combined_context

# Main UI
st.title("🤖 RAG Agent")

# Sidebar for data ingestion
with st.sidebar:
    st.header("Data Ingestion")
    
    uploaded_file = st.file_uploader("Upload a document", type=["pdf", "txt"])
    
    if uploaded_file is not None:
        # Create upload directory if it doesn't exist
        os.makedirs("./data/uploads", exist_ok=True)
        
        # Save the file
        with open(f"./data/uploads/{uploaded_file.name}", "wb") as f:
            f.write(uploaded_file.getbuffer())
        
        if st.button("Process Document"):
            with st.spinner("Processing document..."):
                # Process the document
                if uploaded_file.name.endswith(".pdf"):
                    loader = PyPDFLoader(f"./data/uploads/{uploaded_file.name}")
                    documents = loader.load()
                else:
                    loader = TextLoader(f"./data/uploads/{uploaded_file.name}")
                    documents = loader.load()
                
                # Split documents
                text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
                chunks = text_splitter.split_documents(documents)
                
                # Add to vector store
                vector_store.add_documents(chunks)
                
                st.success(f"Added {len(chunks)} chunks to the vector store!")

# Main chat interface
if "messages" not in st.session_state:
    st.session_state.messages = [{"role": "assistant", "content": "Ask me a question about your documents!"}]

for message in st.session_state.messages:
    with st.chat_message(message["role"]):
        st.write(message["content"])

if prompt := st.chat_input("What would you like to know?"):
    st.session_state.messages.append({"role": "user", "content": prompt})
    with st.chat_message("user"):
        st.write(prompt)
    
    with st.chat_message("assistant"):
        with st.spinner("Thinking..."):
            response, context = strategic_multi_query_rag(prompt)
            st.write(response)
            with st.expander("View retrieved context"):
                st.write(context)
    
    st.session_state.messages.append({"role": "assistant", "content": response})