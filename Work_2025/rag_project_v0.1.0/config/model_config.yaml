# Model configuration
llm:
  provider: ollama
  base_url: http://ollama2:11434
  models:
    default: llama3:8b # change if you want to switch models
    alternatives:
      - mistral
      - gemma:2b
  parameters:
    temperature: 0.3
    max_tokens: 1000

embeddings:
  provider: ollama
  base_url: http://ollama2:11434
  model: llama3:8b

vectorstore:
  provider: chroma
  persist_directory: ./data/vector_stores/chroma_db
  distance_function: cosine

document_processing:
  chunk_size: 1000
  chunk_overlap: 200
