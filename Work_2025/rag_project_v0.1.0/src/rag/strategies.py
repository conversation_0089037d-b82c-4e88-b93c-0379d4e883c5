"""RAG strategies implementation."""
from typing import Dict, Any, List, Optional
import yaml
import os

from src.llm.ollama_client import OllamaClient
from src.rag.retriever import DocumentRetriever

class StrategicMultiQueryRAG:
    """Strategic multi-query RAG implementation."""
    
    def __init__(self, llm_client=None, retriever=None, config_path: str = "config/model_config.yaml"):
        """Initialize the strategic RAG system."""
        # Load LLM client if not provided
        if llm_client is None:
            self.llm_client = OllamaClient.load_ollama_from_config(config_path)
        else:
            self.llm_client = llm_client
        
        # Load document retriever if not provided
        if retriever is None:
            self.retriever = DocumentRetriever(config_path)
        else:
            self.retriever = retriever
        
        # Load prompt templates
        with open("config/prompt_templates.yaml", 'r') as f:
            templates = yaml.safe_load(f)
        
        self.templates = templates.get('rag', {})
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from file."""
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
    
    def generate_search_strategies(self, question: str) -> Dict[str, str]:
        """Generate multiple search strategies for a question."""
        strategy_prompt = self.templates.get('strategy_generation', '').format(question=question)
        strategies_text = self.llm_client.generate(strategy_prompt)
        
        # Parse strategies
        strategies = {}
        for strategy_type in ["BROAD", "TECHNICAL", "COMPARATIVE"]:
            if f"{strategy_type}:" in strategies_text:
                search_terms = strategies_text.split(f"{strategy_type}:")[1].split("\n")[0].strip()
                strategies[strategy_type] = search_terms
        
        return strategies
    
    def execute_search_strategies(self, strategies: Dict[str, str], k: int = 3) -> Dict[str, Dict[str, Any]]:
        """Execute search for each strategy."""
        search_results = {}
        
        for strategy_type, search_terms in strategies.items():
            _, context = self.retriever.get_relevant_documents(search_terms, k=k)
            search_results[strategy_type] = {
                "query": search_terms,
                "context": context
            }
        
        return search_results
    
    def synthesize_results(self, question: str, search_results: Dict[str, Dict[str, Any]]) -> str:
        """Synthesize results from multiple searches."""
        # Combine contexts
        combined_context = ""
        for strategy_type, result in search_results.items():
            combined_context += f"\n\n=== {strategy_type} SEARCH ===\n"
            combined_context += f"Query: {result['query']}\n"
            combined_context += f"Results: {result['context']}\n"
        
        # Generate synthesis
        synthesis_prompt = self.templates.get('synthesis', '').format(
            question=question,
            combined_context=combined_context
        )
        
        return self.llm_client.generate(synthesis_prompt)
    
    def query(self, question: str, k: int = 3) -> Dict[str, Any]:
        """
        Execute full strategic RAG pipeline.
        
        Args:
            question: User question
            k: Number of documents to retrieve per strategy
            
        Returns:
            Dictionary with final answer and intermediate results
        """
        # Step 1: Generate search strategies
        strategies = self.generate_search_strategies(question)
        
        # Step 2: Execute searches
        search_results = self.execute_search_strategies(strategies, k)
        
        # Step 3: Synthesize results
        final_answer = self.synthesize_results(question, search_results)
        
        return {
            "question": question,
            "strategies": strategies,
            "search_results": search_results,
            "final_answer": final_answer
        }
