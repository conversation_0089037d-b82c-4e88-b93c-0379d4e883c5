"""Check environment variables and connections."""
import os
import sys
import requests

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

def main():
    """Check environment variables and connections."""
    print("Environment Variables:")
    print(f"OLLAMA_URL: {os.environ.get('OLLAMA_URL', 'Not set')}")
    
    # Ensure OLLAMA_URL has protocol
    ollama_url = os.environ.get("OLLAMA_URL", "http://localhost:11434")
    if not ollama_url.startswith(('http://', 'https://')):
        ollama_url = f"http://{ollama_url}"
        os.environ["OLLAMA_URL"] = ollama_url
        print(f"Updated OLLAMA_URL: {os.environ['OLLAMA_URL']}")
    
    # Test connection to Ollama
    try:
        response = requests.get(f"{ollama_url}/api/tags")
        print(f"Connection to Ollama successful: {response.status_code}")
        print(f"Available models: {response.json()}")
    except Exception as e:
        print(f"Error connecting to Ollama: {e}")

if __name__ == "__main__":
    main()