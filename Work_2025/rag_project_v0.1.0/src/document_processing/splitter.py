"""Text splitting functionality."""
from typing import List, Dict, Any
import yaml

from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_core.documents import Document

class DocumentSplitter:
    """Splits documents into chunks."""
    
    def __init__(self, config_path: str = "config/model_config.yaml"):
        """Initialize the document splitter."""
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        doc_config = config.get('document_processing', {})
        self.chunk_size = doc_config.get('chunk_size', 1000)
        self.chunk_overlap = doc_config.get('chunk_overlap', 200)
        
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.chunk_size, 
            chunk_overlap=self.chunk_overlap
        )
    
    def split_documents(self, documents: List[Document]) -> List[Document]:
        """
        Split documents into chunks.
        
        Args:
            documents: List of documents to split
            
        Returns:
            List of document chunks
        """
        return self.text_splitter.split_documents(documents)