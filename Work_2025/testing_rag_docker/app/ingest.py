import os
from dotenv import load_dotenv
from langchain_ollama import OllamaEmbeddings
from langchain_chroma import Chroma
from langchain_community.document_loaders import PyPDFLoader, TextLoader, DirectoryLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
import argparse

# Load environment variables
load_dotenv()

def initialize_embedding_model():
    embedding_model = OllamaEmbeddings(
        base_url=os.environ.get("OLLAMA", "http://localhost:11434"),
        model="gemma:2b",
    )
    return embedding_model
# to add new models use this docker compose exec ollama ollama pull MODEL_NAME
def process_documents(directory_path):
    # Initialize embedding model
    embedding_model = initialize_embedding_model()
    
    # Create data directories if they don't exist
    os.makedirs("./data/uploads", exist_ok=True)
    os.makedirs("./data/chroma_db", exist_ok=True)
    
    # Load documents
    loader = DirectoryLoader(
        directory_path,
        glob="**/*.*",
        loader_cls={
            ".pdf": PyPDFLoader,
            ".txt": TextLoader
        }
    )
    
    documents = loader.load()
    print(f"Loaded {len(documents)} documents")
    
    # Split documents
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
    chunks = text_splitter.split_documents(documents)
    print(f"Split into {len(chunks)} chunks")
    
    # Create or update vector store
    vector_store = Chroma(
        persist_directory="./data/chroma_db",
        embedding_function=embedding_model
    )
    
    # Add documents to the vector store
    vector_store.add_documents(chunks)
    vector_store.persist()
    
    print(f"Added {len(chunks)} chunks to vector store")
    
    # Test retrieval
    test_query = "test query"
    results = vector_store.similarity_search_with_score(test_query, k=1)
    print(f"\nTest Query: '{test_query}'")
    print(f"Retrieved Document Score: {results[0][1]:.4f}")
    print(f"Content: {results[0][0].page_content[:200]}...")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Ingest documents into the vector store")
    parser.add_argument("--dir", type=str, default="./data/documents", help="Directory containing documents to ingest")
    args = parser.parse_args()
    
    process_documents(args.dir)