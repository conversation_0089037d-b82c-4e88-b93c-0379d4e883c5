services:
  ollama:
    image: ollama/ollama
    container_name: ollama2
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
      - ~/.aws:/root/.aws:ro
    networks:
      - rag_network
    healthcheck:
      test: ["CMD-SHELL", "ps aux | grep 'ollama serve' || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s
      



  rag_app:
    build:
      context: ..
      dockerfile: docker/app.Dockerfile
    container_name: rag_app2
    ports:
      - "8501:8501"
    environment:
      - OLLAMA_URL=${OLLAMA_URL:-http://ollama2:11434}
      - OLLAMA_MODEL=${OLLAMA_MODEL:-llama3:8b} #change model here
      - DEBUG=${DEBUG:-false}
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - S3_BUCKET=${S3_BUCKET:-}
      - S3_PREFIX=${S3_PREFIX:-uploads/}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID:-}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY:-}
      - AWS_REGION=${AWS_REGION:-us-east-1}
      - VECTOR_DB_PATH=${VECTOR_DB_PATH:-./data/vector_stores/chroma_db}
      - CHUNK_SIZE=${CHUNK_SIZE:-1000}
      - CHUNK_OVERLAP=${CHUNK_OVERLAP:-200}
      - TEMPERATURE=${TEMPERATURE:-0.3}
      - MAX_TOKENS=${MAX_TOKENS:-1000}
    depends_on:
      ollama:
        condition: service_healthy
    volumes:
      - ../data:/app/data
      - ../src:/app/src     # NEW: live-mount the UI code
      - ../scripts:/app/scripts  # NEW: if check_env.py changes
      - ../.env:/app/.env  # Mount the .env file into the container
      - ~/.aws:/root/.aws:ro
    env_file:
      - ../.env

    networks:
      - rag_network
  watcher:
    build:
      context: ..
      dockerfile: docker/app.Dockerfile
    container_name: rag_watcher
    command: python3 scripts/watch_uploads.py
    volumes:
      - ../data:/data       # Mount data folder to watch uploads
      - ../src:/app/src          # If needed by the script
      - ../scripts:/app/scripts  # So watch_uploads.py is accessible
    depends_on:
      - rag_app
    networks:
      - rag_network
    env_file:
    - ../.env
  s3_ingest:
    build:
      context: ..
      dockerfile: docker/app.Dockerfile
    container_name: rag_s3_ingest
    entrypoint: python3 scripts/s3_ingest.py
    volumes:
      - ../data:/app/data
      - ../src:/app/src
      - ../scripts:/app/scripts
      - ~/.aws:/root/.aws:rw
      - ~/.aws/sso:/root/.aws/sso:rw          # NEW
      - ~/.aws/config:/root/.aws/config:ro    # NEW
      - ~/.aws/cli/cache:/root/.aws/cli/cache # Optional: needed for older CLI behavior
    environment:
      - AWS_PROFILE=charter-intern             # NEW → enables SSO profile
    env_file:
      - ../.env
    networks:
      - rag_network
    restart: on-failure

networks:
  rag_network:
    driver: bridge
    name: rag_network


volumes:
  ollama_data:
