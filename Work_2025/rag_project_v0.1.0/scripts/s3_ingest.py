import os
import boto3
from botocore.exceptions import NoCredentialsError
from pathlib import Path
import sys
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

sys.path.insert(0, str(Path(__file__).parent.parent))
from scripts.ingest import ingest_documents

S3_BUCKET = os.getenv("S3_BUCKET")
S3_PREFIX = os.getenv("S3_PREFIX", "uploads/")
LOCAL_DIR = os.getenv("LOCAL_UPLOAD_DIR", "./data/uploads")

session = boto3.Session(profile_name="charter-intern")
s3 = session.client("s3")


def download_new_files():
    try:
        Path(LOCAL_DIR).mkdir(parents=True, exist_ok=True)
        response = s3.list_objects_v2(Bucket=S3_BUCKET, Prefix=S3_PREFIX)
        if "Contents" not in response:
            return []

        new_files = []
        for obj in response["Contents"]:
            s3_key = obj["Key"]
            file_name = s3_key.split("/")[-1]
            local_path = os.path.join(LOCAL_DIR, file_name)

            # Skip already downloaded files
            if os.path.exists(local_path):
                continue

            s3.download_file(S3_BUCKET, s3_key, local_path)
            print(f"Downloaded {s3_key} → {local_path}")
            new_files.append(local_path)

        return new_files
    except NoCredentialsError:
        print("AWS credentials not found.")
        return []
    except Exception as e:
        print(f"Error in download_new_files: {e}")
        return []
    


def run_ingestion_once():
    print(f"Starting one-time S3 ingestion for bucket: {S3_BUCKET}, prefix: {S3_PREFIX}")
    files = download_new_files()
    if files:
        print(f"Ingesting {len(files)} new files...")
        ingest_documents(LOCAL_DIR)
    else:
        print("No new files to ingest.")

if __name__ == "__main__":
    run_ingestion_once()
